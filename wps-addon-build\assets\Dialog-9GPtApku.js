import{U as p,_ as u,k as m,j as k,c as b,a as e,p as a,t as r,o as w}from"./index-vQcZ5Q3f.js";function g(i,t){switch(i){case"getDocName":{let n=window.Application.ActiveDocument;return n?n.Name:"当前没有打开任何文档"}case"createTaskPane":{let n=window.Application.PluginStorage.getItem("taskpane_id");if(n){let o=window.Application.GetTaskPane(n);o.Visible=!0}else{let o=window.Application.CreateTaskPane(p.GetUrlPath()+"/taskpane"),d=o.ID;window.Application.PluginStorage.setItem("taskpane_id",d),o.Visible=!0}break}case"newDoc":{window.Application.Documents.Add();break}case"addString":{let n=window.Application.ActiveDocument;if(n){n.Range(0,0).Text="Hello, wps加载项!";let o=window.Application.Selection.Range;o&&o.Select()}break}case"closeDoc":{if(window.Application.Documents.Count<2){alert("当前只有一个文档，别关了。");break}let n=window.Application.ActiveDocument;n&&n.Close();break}}}const c={onbuttonclick:g},v={name:"Dialog",data(){return{DemoSpan:"",docName:""}},methods:{onbuttonclick(i){return c.onbuttonclick(i)},onDocNameClick(){this.docName=c.onbuttonclick("getDocName")},onOpenWeb(){c.onbuttonclick("openWeb",this.DemoSpan)}}};m(()=>{k.get("/.debugTemp/NotifyDemoUrl").then(i=>{(void 0).DemoSpan=i.data})});const D={class:"hello"},f={class:"global"},A={class:"divItem"},S={class:"divItem"},x={class:"divItem"};function C(i,t,n,o,d,l){return w(),b("div",D,[e("div",f,[t[8]||(t[8]=e("div",{class:"divItem"},[a(" 这是一个网页，按"),e("span",{style:{"font-weight":"bolder"}},'"F12"'),a("可以打开调试器。 ")],-1)),e("div",A,[t[6]||(t[6]=a(" 这个示例展示了wps加载项的相关基础能力，与B/S业务系统的交互，请用浏览器打开： ")),e("span",{style:{"font-weight":"bolder",color:"slateblue",cursor:"pointer"},onClick:t[0]||(t[0]=s=>l.onOpenWeb())},r(d.DemoSpan),1)]),t[9]||(t[9]=e("div",{class:"divItem"},[a(" 开发文档: "),e("span",{style:{"font-weight":"bolder",color:"slateblue"}},"https://open.wps.cn/docs/office")],-1)),t[10]||(t[10]=e("hr",null,null,-1)),e("div",S,[e("button",{style:{margin:"3px"},onClick:t[1]||(t[1]=s=>l.onDocNameClick())},"取文件名"),e("button",{style:{margin:"3px"},onClick:t[2]||(t[2]=s=>l.onbuttonclick("createTaskPane"))},"创建任务窗格"),e("button",{style:{margin:"3px"},onClick:t[3]||(t[3]=s=>l.onbuttonclick("newDoc"))},"新建文件"),e("button",{style:{margin:"3px"},onClick:t[4]||(t[4]=s=>l.onbuttonclick("addString"))},"文档开头添加字符串"),e("button",{style:{margin:"3px"},onClick:t[5]||(t[5]=s=>l.onbuttonclick("closeDoc"))},"关闭文件")]),t[11]||(t[11]=e("hr",null,null,-1)),e("div",x,[t[7]||(t[7]=a(" 文档文件名为：")),e("span",null,r(d.docName),1)])])])}const y=u(v,[["render",C],["__scopeId","data-v-b2ead4bd"]]);export{y as default};
