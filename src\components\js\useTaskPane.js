import { ref, reactive, onMounted, watch } from 'vue'
import axios from 'axios'
import taskPane from './taskpane.js'
import wsClient from './wsClient.js'
import sha1 from 'js-sha1';
import versionManager from './versionManager.js' // 新增导入 versionManager

function getApiBaseUrl() {
  if (import.meta.env.MODE === 'development') {
    return '';
  } else {
    return 'http://worksheet.hexinedu.com';
  }
}

// 添加服务器API基础URL函数
function getServerUrl() {
  if (import.meta.env.MODE === 'development') {
    return 'http://127.0.0.1:3000';
  } else {
    return 'http://127.0.0.1:3000'; // 生产环境可能需要调整此URL
  }
}

function getRandomValues() {
  let d = new Date().getTime(); // 使用时间戳作为初始种子的一部分（可选，增加一点变化）
  // 如果 performance.now() 可用，它能提供更高精度的时间
  if (typeof performance !== 'undefined' && typeof performance.now === 'function') {
    d += performance.now(); //use high-precision timer if available
  }
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (d + Math.random() * 16) % 16 | 0; // 核心随机部分
    d = Math.floor(d / 16); // 确保 d 每次都变化
    return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);
  });
}

// 添加一个工具函数，支持WebSocket和HTTP请求降级
const requestWithFallback = async (wsMethod, httpMethod, httpUrl, httpParams = {}, timeout = 8000) => {
  try {
    // 先尝试WebSocket请求，但不需要这里手动连接，让wsMethod内部去处理连接
    return await Promise.race([
      wsMethod(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('WebSocket请求超时，切换到HTTP')), timeout)
      )
    ]);
  } catch (wsError) {
    // WebSocket请求失败，降级为HTTP请求
    try {
      let response;
      if (httpMethod === 'get') {
        response = await axios.get(httpUrl, { params: httpParams });
      } else if (httpMethod === 'post') {
        response = await axios.post(httpUrl, httpParams);
      } else if (httpMethod === 'delete') {
        response = await axios.delete(httpUrl);
      }
      return response.data;
    } catch (httpError) {
      throw new Error(`请求失败: ${httpError.message || '未知错误'}`);
    }
  }
};

function generateSignature(appKey, appNonstr, appSecret, appTimestamp) {
  const dataToHash = [appKey, appNonstr, appSecret, appTimestamp].join(':');
  return sha1(dataToHash);
}

export function useTaskPane() {
  // Reactive state
  const docName = ref('');
  const selected = ref('');
  const logger = ref('');
  const map = reactive({}); // 记录一下解析/校对的情况
  const docId = ref('');
  const windowId = ref('');

  // 日志同步相关状态
  let lastLoggerValue = '';
  let logSyncTimer = null;
  // 添加监控目录状态
  const watchedDir = ref('c:\\Temp');
  // 添加appKey相关状态
  const appKeyInfo = reactive({
    appKey: '',
    appSecret: ''
  });

  // 添加自定义弹窗相关状态
  const confirmDialog = reactive({
    show: false,
    message: '',
    resolveCallback: null,
    rejectCallback: null
  });

  // 添加错误弹窗相关状态
  const errorDialog = reactive({
    show: false,
    title: '',
    message: '',
    type: 'error' // 'error', 'warning', 'info'
  });

  // 添加学科和年级的状态
  const subject = ref('');
  const stage = ref('junior');

  // 添加企业ID状态用于控制校对功能可见性
  const enterpriseId = ref(null);

  // 校对功能可见性计算
  const isCheckingVisible = ref(false);

  // 批量测试模式状态
  const isBatchTestMode = ref(false);

  // 批量插入函数
  const batchInsertAllTasks = async () => {
    const batchWaitingTasks = Object.keys(map).filter(tid => map[tid].status === 5);

    if (batchWaitingTasks.length === 0) {
      logger.value += `<span class="log-item warning">没有等待批量插入的任务</span><br/>`;
      return;
    }

    logger.value += `<span class="log-item info">开始批量插入 ${batchWaitingTasks.length} 个任务</span><br/>`;

    // 为所有等待的任务设置固定的测试文件路径，然后改为进行中状态
    batchWaitingTasks.forEach(tid => {
      if (map[tid]) {
        // 确保设置了 resultFile
        if (!map[tid].resultFile) {
          map[tid].resultFile = "C:\\ww-wps-addon\\Downloads\\682ad7b4bde591ec60b7cf56.wps.docx";
        }
        map[tid].status = 1; // 改为进行中状态，让 runschedule 自动处理
        logger.value += `<span class="log-item info">任务 ${tid.substring(0, 8)} 开始插入文档</span><br/>`;
      }
    });
  };

  // 获取等待批量完成的任务数量
  const getBatchWaitingTasksCount = () => {
    return Object.keys(map).filter(tid => map[tid].status === 5).length;
  };

  // 添加学科和年级的选项列表
  const subjectOptions = [
    { value: 'english', label: '英语' },
    { value: 'chinese', label: '语文' },
    { value: 'math', label: '数学' },
    { value: 'physics', label: '物理' },
    { value: 'chemistry', label: '化学' },
    { value: 'biology', label: '生物' },
    { value: 'daode_fazhi', label: '道德与法治' },
    { value: 'history', label: '历史' },
    { value: 'geography', label: '地理' },
  ];

  // 根据版本类型动态设置年级选项
  const updateStageOptions = () => {
    if (versionManager.isSeniorEdition()) {
      return [
        { value: 'senior', label: '高中' },
      ];
    } else {
      return [
        { value: 'junior', label: '初中' },
      ];
    }
  };

  // 初始化年级选项
  const stageOptions = reactive(updateStageOptions());

  // 添加获取监控目录的函数
  const fetchWatchedDir = async () => {
    try {
      // 使用新的降级请求函数
      const response = await wsClient.getWatcherStatus();
      if (response.data && response.data.watchDir) {
        watchedDir.value = response.data.watchDir;
        logger.value += `<span class="log-item info">已获取监控目录: ${watchedDir.value}</span><br/>`;
      }
    } catch (error) {
      logger.value += `<span class="log-item error">获取监控目录失败: ${error.message}</span><br/>`;
      console.error('获取监控目录失败:', error);
    }
  };

  // 修改getAccessToken函数使用保存的appKey和PluginStorage缓存token
  const getAccessToken = async () => {
    try {
      if (!appKeyInfo.appKey || !appKeyInfo.appSecret) {
        throw new Error('未初始化app信息');
      }

      // 添加一个边界值(秒)，提前这么多秒认为token过期
      const expirationBuffer = 60; // 提前60秒认为过期
      const currentTime = Date.now();

      // 从PluginStorage获取token信息
      let tokenInfo;
      try {
        const tokenInfoStr = window.Application.PluginStorage.getItem('token_info');
        if (tokenInfoStr) {
          tokenInfo = JSON.parse(tokenInfoStr);
        }
      } catch (e) {
        // 如果解析失败，视为没有缓存
        tokenInfo = null;
        logger.value += `<span class="log-item warning">解析缓存token失败: ${e.message}</span><br/>`;
      }

      // 检查是否有缓存的有效token
      if (tokenInfo && tokenInfo.access_token &&
        tokenInfo.expired_time > currentTime + (expirationBuffer * 1000)) {
        // 返回缓存的token
        return tokenInfo.access_token;
      }

      // 请求新token
      const app_key = appKeyInfo.appKey;
      const app_nonstr = "1234567";
      // 10 位时间戳
      const app_timestamp = Math.floor(Date.now() / 1000);
      // @todo: 注意：实际场景中应该在服务端动态生成app_signature
      // 将 app_key, app_nonstr, app_secret, app_timestamp 以:分隔拼接，利用sha1 hash算法生成
      const app_signature = generateSignature(app_key, app_nonstr, appKeyInfo.appSecret, app_timestamp);

      const response = await axios.get(getApiBaseUrl() + `/api/open/account/v1/auth/token`, {
        params: {
          app_key,
          app_nonstr,
          app_timestamp,
          app_signature
        },
      });

      if (response.data?.data?.access_token) {
        const newToken = response.data.data.access_token;

        // 计算token过期时间(毫秒)
        let expiredTime;
        if (response.data.data.expired_time) {
          const expiredSeconds = parseInt(response.data.data.expired_time);
          expiredTime = currentTime + (expiredSeconds * 1000);
          logger.value += `<span class="log-item info">token已更新，有效期${expiredSeconds}秒</span><br/>`;
        } else {
          // 如果没有过期时间信息，设置默认过期时间(1小时)
          expiredTime = currentTime + (3600 * 1000);
          logger.value += `<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>`;
        }

        // 将token信息保存到PluginStorage
        const tokenInfoToSave = {
          access_token: newToken,
          expired_time: expiredTime
        };

        try {
          window.Application.PluginStorage.setItem('token_info', JSON.stringify(tokenInfoToSave));
        } catch (e) {
          logger.value += `<span class="log-item warning">保存token到PluginStorage失败: ${e.message}</span><br/>`;
        }

        return newToken;
      } else {
        throw new Error(response.data?.message || '获取access_token失败');
      }
    } catch (error) {
      logger.value += `<span class="log-item error">获取access_token失败: ${error.message}</span><br/>`;
      throw error;
    }
  };

  // Methods
  const clearLog = () => {
    logger.value = '<span class="log-item info">日志已清空</span><br/>'
  };

  const getCurrentDocument = () => {
    const app = window.Application
    const count = app.Documents.Count;
    for (let i = 1; i <= count; i++) {
      const document = app.Documents.Item(i)
      if (document.DocID === docId.value) {
        return document
      }
    }
    return null
  }

  // 检查文档格式的函数
  const checkDocumentFormat = () => {
    try {
      const document = getCurrentDocument()
      if (!document) {
        return { isValid: false, message: '未找到当前文档' }
      }

      // 获取文档名称
      let docName = ''
      try {
        docName = document.Name || ''
      } catch (error) {
        // 如果无法获取文档名称，尝试通过其他方式
        try {
          docName = onbuttonclick('getDocName') || ''
        } catch (e) {
          docName = ''
        }
      }

      // 检查文档格式
      if (docName) {
        const lowerName = docName.toLowerCase()

        // 检查是否为 .docx 格式
        if (lowerName.endsWith('.docx')) {
          return { isValid: true, message: '文档格式正确' }
        }

        // 检查是否为 .doc 格式
        if (lowerName.endsWith('.doc')) {
          return {
            isValid: false,
            message: '当前文档是 .doc 格式，该插件只能服务于 .docx 文件。\n\n建议操作：\n1. 点击"文件" → "另存为"\n2. 在"保存类型"中选择"Word 文档(*.docx)"\n3. 保存后重新打开 .docx 文件'
          }
        }

        // 其他格式
        return {
          isValid: false,
          message: '该插件只能服务于 .docx 文件，当前文档格式不支持。\n\n请使用 .docx 格式的文档。'
        }
      }

      // 无法获取文档名称时的处理
      return {
        isValid: false,
        message: '无法确定文档格式，请确保当前文档已保存为 .docx 格式。'
      }

    } catch (error) {
      console.error('检查文档格式时出错:', error)
      return {
        isValid: false,
        message: '检查文档格式时出错，请确保当前文档已保存为 .docx 格式。'
      }
    }
  }

  // 为选中文本或关键字添加批注（支持指定range）
  const addCommentToSelection = async (commentText, keyword = '', index = 0, targetRange = null) => {
    return await withVBAOperationWrapper('添加批注', async () => {
      const app = window.Application
      const document = getCurrentDocument();

      // 如果提供了targetRange，使用它；否则使用当前选区
      let workingRange;
      if (targetRange) {
        workingRange = targetRange;
      } else {
        const selection = document.ActiveWindow.Selection;
        if (!selection || selection.Text === '') {
          logger.value += '<span class="log-item error">请先选择文本</span><br/>'
          return false
        }
        workingRange = selection.Range;
      }
      // 默认为整个range添加批注
      if (!keyword) {
        // 正确创建批注
        const comment = document.Comments.Add(workingRange, commentText)
        // 设置批注内容的段落格式 - 行间距为1
        try {
          if (comment && comment.Range && comment.Range.ParagraphFormat) {
            comment.Range.ParagraphFormat.Reset()
            comment.Range.ParagraphFormat.LineSpacingRule = 3;
            comment.Range.ParagraphFormat.LineSpacing = 10;
          }
        } catch (formatError) {
          logger.value += `<span class="log-item warning">设置批注段落格式失败: ${formatError.message}</span><br/>`
        }

        logger.value += `<span class="log-item success">已为${targetRange ? '指定范围' : '选中内容'}添加批注: "${commentText}"</span><br/>`
        return true
      } else {
        // 在指定范围中查找关键字并为每个匹配项添加批注
        const rangeText = workingRange.Text

        // 创建Find对象用于查找关键字 - 限制在工作范围内
        const find = workingRange.Find;
        find.ClearFormatting()
        find.Text = keyword
        find.Forward = true
        find.Wrap = 0 // wdFindStop - 不循环查找，避免超出范围

        let foundCount = 0
        let processedRanges = [] // 用于跟踪已处理的范围

        // 记录原始工作范围的边界
        const originalStart = workingRange.Start
        const originalEnd = workingRange.End

        // 在范围内查找所有匹配项并添加批注
        while (find.Execute()) {
          // 严格确保查找结果在原始工作范围内
          if (find.Found &&
            find.Parent.Start >= originalStart &&
            find.Parent.End <= originalEnd) {

            // 检查当前找到的范围是否已经处理过
            const currentStart = find.Parent.Start
            const currentEnd = find.Parent.End

            // 检查是否与已处理的范围重叠
            const isDuplicate = processedRanges.some(range =>
              (currentStart === range.start && currentEnd === range.end)
            )

            if (!isDuplicate) {
              // 记录已处理的范围
              processedRanges.push({
                start: currentStart,
                end: currentEnd
              })

              // 如果index为-1，则为所有关键词添加批注
              // 或者当前是指定的索引位置，则添加批注
              if (index === -1 || foundCount === index) {
                // 使用找到的范围创建批注
                const comment = document.Comments.Add(find.Parent, commentText)

                // 设置批注内容的段落格式 - 行间距为1
                try {
                  if (comment && comment.Range && comment.Range.ParagraphFormat) {
                    comment.Range.ParagraphFormat.Reset()
                    comment.Range.ParagraphFormat.LineSpacingRule = 3;
                    comment.Range.ParagraphFormat.LineSpacing = 10;
                  }
                } catch (formatError) {
                  logger.value += `<span class="log-item warning">设置批注段落格式失败: ${formatError.message}</span><br/>`
                }

                // 如果只处理特定索引，且已找到，则可以退出循环
                if (index !== -1 && foundCount === index) {
                  return true
                }
              }

              foundCount++

              // 移动查找位置到当前匹配之后，但确保不超出原始范围
              const nextStart = Math.min(currentEnd, originalEnd)
              console.log('nextStart', nextStart)
              if (nextStart >= originalEnd) {
                break // 已到达范围末尾
              }

              // 重新设置查找范围，确保不超出原始边界
              const remainingRange = document.Range(nextStart, originalEnd)
              find.Parent.SetRange(nextStart, originalEnd)
            } else {
              // 如果是重复的匹配，移动查找位置
              const nextStart = Math.min(currentEnd, originalEnd)
              if (nextStart >= originalEnd) {
                break // 已到达范围末尾
              }
              find.Parent.SetRange(nextStart, originalEnd)
            }
          } else {
            // 查找结果超出范围或未找到，退出循环
            break
          }
        }

        if (index !== -1 && foundCount <= index) {
          return false
        }

        if (index === -1 && foundCount > 0) {
          return true
        } else if (index === -1 && foundCount === 0) {
          return false
        }

        return true
      }
    });
  };

  const getTaskStatusClass = (status) => {
    if (status === 0) return 'status-preparing'
    if (status === 1) return 'status-running'
    if (status === 2) return 'status-completed'
    if (status === -1) return 'status-error'
    if (status === 3) return 'status-released'
    if (status === 4) return 'status-stopped'
    if (status === 5) return 'status-batch-waiting'
    return ''
  };

  const getTaskStatusText = (status) => {
    if (status === 0) return '准备中'
    if (status === 1) return '进行中'
    if (status === 2) return '已完成'
    if (status === -1) return '异常'
    if (status === 3) return '已释放'
    if (status === 4) return '已停止'
    if (status === 5) return '等待批量插入'
    return '准备中'
  };

  const getElapsedTime = (startTime) => {
    const elapsed = Date.now() - startTime
    const seconds = Math.floor(elapsed / 1000)

    if (seconds < 60) {
      return `${seconds}秒`
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
    } else {
      return `${Math.floor(seconds / 3600)}时${Math.floor((seconds % 3600) / 60)}分`
    }
  };

  // 添加一个新函数：停止任务但保留控件
  const stopTaskWithoutRemovingControl = async (tid) => {
    try {
      // 检查任务是否存在
      if (!map[tid]) {
        logger.value += `<span class="log-item error">找不到任务${tid.substring(0, 8)}的数据</span><br/>`;
        return;
      }

      // 更新任务状态
      map[tid].status = 4; // 4 for "已停止" (Stopped)
      map[tid].terminated = true;
      map[tid].errorMessage = '用户选择不继续';

      // 更新控件标题为"已停止"状态，但不删除控件
      try {
        const currentDocument = getCurrentDocument();
        if (currentDocument && currentDocument.ContentControls) {
          for (let i = 1; i <= currentDocument.ContentControls.Count; i++) {
            try {
              const control = currentDocument.ContentControls.Item(i);
              if (control && control.Title && (
                control.Title === `任务_${tid}` ||
                control.Title === `任务增强_${tid}` ||
                control.Title === `校对_${tid}`
              )) {
                // 检查控件类型
                const isEnhanced = control.Title === `任务增强_${tid}` || map[tid].isEnhanced;
                const isCheckTask = control.Title === `校对_${tid}` || map[tid].isCheckTask;

                // 更新控件标题为已停止状态
                if (isCheckTask) {
                  control.Title = `已停止校对_${tid}`;
                } else if (isEnhanced) {
                  control.Title = `已停止增强_${tid}`;
                } else {
                  control.Title = `已停止_${tid}`;
                }

                const taskType = isCheckTask ? '校对' : (isEnhanced ? '增强' : '普通');
                logger.value += `<span class="log-item info">已将${taskType}任务${tid.substring(0, 8)}控件标记为已停止（保留控件）</span><br/>`;
                break;
              }
            } catch (e) {
              continue; // 忽略单个控件错误
            }
          }
        }
      } catch (titleUpdateError) {
        logger.value += `<span class="log-item warning">更新控件标题失败: ${titleUpdateError.message}</span><br/>`;
      }

      // 停止相关的URL监控
      let urlIdToStop = null;
      if (urlMonitorTasks[tid] && urlMonitorTasks[tid].urlId) {
        urlIdToStop = urlMonitorTasks[tid].urlId;

        try {
          // 首先检查是否有已下载的文件
          try {
            const urlStatusResponse = await requestWithFallback(
              async () => await wsClient.getUrlMonitorStatus(),
              'get',
              `${getServerUrl()}/api/url/status`
            );

            // 处理可能的不同响应格式
            const urlInfoList = Array.isArray(urlStatusResponse) ?
              urlStatusResponse :
              (urlStatusResponse.data ?
                (Array.isArray(urlStatusResponse.data) ? urlStatusResponse.data : []) :
                []);

            const urlInfo = urlInfoList.find(info => info.urlId === urlIdToStop);

            if (urlInfo && urlInfo.downloadedPath) {
              // 更新任务的结果文件路径
              map[tid].resultFile = urlInfo.downloadedPath;
              map[tid].resultDownloaded = true;
              logger.value += `<span class="log-item success">发现任务${tid.substring(0, 8)}已下载文件: ${urlInfo.downloadedPath}</span><br/>`;
            }
          } catch (checkError) {
            logger.value += `<span class="log-item warning">检查URL下载状态出错: ${checkError.message}</span><br/>`;
          }

          // 确保强制停止监控
          logger.value += `<span class="log-item info">停止任务${tid.substring(0, 8)}的URL监控</span><br/>`;
          await stopUrlMonitoring(urlIdToStop);

          // 立即从监控任务列表中移除
          delete urlMonitorTasks[tid];
        } catch (stopError) {
          logger.value += `<span class="log-item warning">停止URL监控出错: ${stopError.message}，将重试</span><br/>`;

          // 即使出错也从列表中移除
          delete urlMonitorTasks[tid];

          // 再次尝试停止监控
          setTimeout(async () => {
            try {
              if (urlIdToStop) {
                await requestWithFallback(
                  async () => await wsClient.stopUrlMonitoring(urlIdToStop),
                  'delete',
                  `${getServerUrl()}/api/url/monitor/${urlIdToStop}`
                );
              }
            } catch (retryError) {
              logger.value += `<span class="log-item warning">重试停止URL监控失败: ${retryError.message}</span><br/>`;
            }
          }, 1000);
        }
      }

      // 注意：这里不调用 tryRemoveTaskPlaceholder，保留控件
      logger.value += `<span class="log-item success">任务${tid.substring(0, 8)}已停止（控件已保留）</span><br/>`;
    } catch (error) {
      logger.value += `<span class="log-item error">停止任务${tid.substring(0, 8)}出错: ${error.message}</span><br/>`;

      // 确保删除监控任务
      if (urlMonitorTasks[tid]) {
        delete urlMonitorTasks[tid];
      }
    }
  };

  const terminateTask = async (tid) => {
    // 检查任务是否存在
    if (!map[tid]) {
      logger.value += `<span class="log-item error">找不到任务${tid.substring(0, 8)}的数据</span><br/>`;
      return;
    }

    // 更新任务状态
    map[tid].status = 4; // 4 for "已停止" (Stopped)
    map[tid].terminated = true;
    map[tid].errorMessage = '用户手动终止';

    // 更新控件标题为"已停止"状态
    const currentDocument = getCurrentDocument();
    if (currentDocument && currentDocument.ContentControls) {
      for (let i = 1; i <= currentDocument.ContentControls.Count; i++) {
        try {
          const control = currentDocument.ContentControls.Item(i);
          if (control && control.Title && (
            control.Title === `任务_${tid}` ||
            control.Title === `任务增强_${tid}` ||
            control.Title === `校对_${tid}`
          )) {
            // 检查控件类型
            const isEnhanced = control.Title === `任务增强_${tid}` || map[tid].isEnhanced;
            const isCheckTask = control.Title === `校对_${tid}` || map[tid].isCheckTask;

            // 更新控件标题为已停止状态
            if (isCheckTask) {
              control.Title = `已停止校对_${tid}`;
            } else if (isEnhanced) {
              control.Title = `已停止增强_${tid}`;
            } else {
              control.Title = `已停止_${tid}`;
            }

            control.LockContents = false;
            const taskType = isCheckTask ? '校对' : (isEnhanced ? '增强' : '普通');
            logger.value += `<span class="log-item info">已将${taskType}任务${tid.substring(0, 8)}控件标记为已停止</span><br/>`;
            break;
          }
        } catch (e) {
          continue; // 忽略单个控件错误
        }
      }
    }

    // 停止相关的URL监控 - 确保在停止监控前记录需要停止的urlId
    let urlIdToStop = null;
    if (urlMonitorTasks[tid] && urlMonitorTasks[tid].urlId) {
      urlIdToStop = urlMonitorTasks[tid].urlId;

      try {
        // 首先检查是否有已下载的文件
        const urlStatusResponse = await wsClient.getUrlMonitorStatus()

        // 处理可能的不同响应格式
        const urlInfoList = Array.isArray(urlStatusResponse) ?
          urlStatusResponse :
          (urlStatusResponse.data ?
            (Array.isArray(urlStatusResponse.data) ? urlStatusResponse.data : []) :
            []);

        const urlInfo = urlInfoList.find(info => info.urlId === urlIdToStop);

        if (urlInfo && urlInfo.downloadedPath) {
          // 更新任务的结果文件路径
          map[tid].resultFile = urlInfo.downloadedPath;
          map[tid].resultDownloaded = true;
          logger.value += `<span class="log-item success">发现任务${tid.substring(0, 8)}已下载文件: ${urlInfo.downloadedPath}</span><br/>`;
        }


        // 确保强制停止监控
        logger.value += `<span class="log-item info">停止任务${tid.substring(0, 8)}的URL监控</span><br/>`;
        await stopUrlMonitoring(urlIdToStop);

        // 立即从监控任务列表中移除
        delete urlMonitorTasks[tid];
      } catch (stopError) {
        logger.value += `<span class="log-item warning">停止URL监控出错: ${stopError.message}，将重试</span><br/>`;
        // 即使出错也从列表中移除
        delete urlMonitorTasks[tid];
      }
    }
  };

  const onbuttonclick = (id) => {
    return taskPane.onbuttonclick(id)
  };

  const getDocumentName = () => {
    try {
      docName.value = onbuttonclick('getDocName') || '未命名文档'
    } catch (error) {
      docName.value = '未命名文档'
    }
  };

  const copy = () => {
    const document = getCurrentDocument();
    document.ActiveWindow.Selection.Copy();
  };

  // 第一种方式：打开新窗口，然后保存，保存后关闭窗口
  const saveDocxBy1 = (uuid) => {
    const newd = window.Application.Documents.Add();
    newd.Content.Paste();
    newd.SaveAs2(`${watchedDir.value}\\${uuid}`, 12, '', '', false);
    // 跳转到当前的 window
    newd.Close();
    const currentDocument = getCurrentDocument();
    currentDocument.ActiveWindow.Activate();
  }

  // 第二种方式：不打开新窗口，然后保存，保存后关闭窗口
  const saveDocxBy2 = (uuid) => {
    const newd = window.Application.Documents.Add('', false, 0, false);
    newd.Content.Paste();
    newd.SaveAs2(`${watchedDir.value}\\${uuid}`, 12, '', '', false);
    newd.Close();
    const currentDocument = getCurrentDocument();
    currentDocument.ActiveWindow.Activate();
  }
  // 第三种方式：使用中转目录保存
  const saveDocxBy3 = (uuid) => {
    try {
      // 获取 %appdata% 路径
      const appDataPath = window.Application.Env.GetAppDataPath();
      const tempDir = `${appDataPath}\\wps-addon-server\\temp_docx`;
      const newd = window.Application.Documents.Add('', false, 0, false);
      newd.Content.Paste();
      const tempFilePath = `${tempDir}\\${uuid}`;
      newd.SaveAs2(tempFilePath, 12, '', '', false);
      newd.Close();
      const currentDocument = getCurrentDocument();
      currentDocument.ActiveWindow.Activate();
      logger.value += `<span class="log-item success">文件已保存到中转目录: ${tempFilePath}.docx</span><br/>`;
    } catch (error) {
      logger.value += `<span class="log-item error">方式三保存失败: ${error.message}</span><br/>`;
      throw error;
    }
  }

  // 第四种方式（合心开启）：打开新窗口，然后保存，保存后不关闭窗口（debug）
  const saveDocxBy4 = (uuid) => {
    const newd = window.Application.Documents.Add();
    newd.Content.Paste();
    newd.SaveAs2(`${watchedDir.value}\\${uuid}`, 12, '', '', false);
    const currentDocument = getCurrentDocument();
    currentDocument.ActiveWindow.Activate();
  }

  const generatedocx = async (uuid) => {
    try {
      logger.value += `<span class="log-item info">开始生成文档: ${uuid}</span><br/>`;

      // 获取当前保存方式设置
      let saveMethod = 'method3'; // 默认方式三
      try {
        const saveMethodResponse = await wsClient.getSaveMethod();
        if (saveMethodResponse.success && saveMethodResponse.saveMethod) {
          saveMethod = saveMethodResponse.saveMethod;
          const methodName = saveMethod === 'method1' ? '方式一' :
            saveMethod === 'method2' ? '方式二' :
              saveMethod === 'method3' ? '方式三' : '方式四';
          logger.value += `<span class="log-item info">使用保存方式: ${methodName}</span><br/>`;
        }
      } catch (error) {
        logger.value += `<span class="log-item warning">获取保存方式失败，使用默认方式三: ${error.message}</span><br/>`;
      }

      // 根据设置的保存方式执行不同的保存逻辑
      if (saveMethod === 'method1') {
        saveDocxBy1(uuid)
        logger.value += `<span class="log-item success">文件已通过方式一保存到监控目录: ${watchedDir.value}\\${uuid}.docx</span><br/>`;
      } else if (saveMethod === 'method2') {
        saveDocxBy2(uuid)
        logger.value += `<span class="log-item success">文件已通过方式二保存到监控目录: ${watchedDir.value}\\${uuid}.docx</span><br/>`;
      } else if (saveMethod === 'method3') {
        saveDocxBy3(uuid)
        logger.value += `<span class="log-item success">文件已通过方式三保存到中转目录，等待后端转移到监控目录</span><br/>`;
      } else if (saveMethod === 'method4') {
        saveDocxBy4(uuid)
        logger.value += `<span class="log-item success">文件已通过方式四保存到监控目录: ${watchedDir.value}\\${uuid}.docx</span><br/>`;
      }

      // 关联文件与客户端ID - 确保文件上传成功事件只发送给当前客户端
      wsClient.associateFileWithClient(`${uuid}.docx`)
        .then(result => {
          if (result.success) {
            logger.value += `<span class="log-item info">文件 ${uuid}.docx 已关联到当前客户端</span><br/>`;
          } else {
            logger.value += `<span class="log-item warning">关联文件失败: ${result.message || '未知错误'}</span><br/>`;
          }
        })
        .catch(error => {
          logger.value += `<span class="log-item warning">关联文件时出错: ${error.message}</span><br/>`;
        });
    } catch (error) {
      logger.value += `<span class="log-item error">保存文件失败: ${error.message}</span><br/>`;
    }

  };

  // 添加WebSocket相关的状态和方法
  const ws = ref(null);
  const wsMessages = reactive([]);

  // 添加一个集合用于记录已处理的事件，避免重复处理
  const processedEvents = new Set();

  // 清理HTML标签，提取纯文本
  const cleanHtmlContent = (htmlContent) => {
    if (!htmlContent || typeof htmlContent !== 'string') {
      return '';
    }

    return htmlContent
      // 将<br/>转换为换行符
      .replace(/<br\s*\/?>/gi, '\n')
      // 移除所有HTML标签，但保留内部文本
      .replace(/<[^>]*>/g, '')
      // 解码HTML实体
      .replace(/&nbsp;/g, ' ')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      // 清理多余的空白字符
      .replace(/\s+/g, ' ')
      // 清理多余的换行符
      .replace(/\n\s*\n/g, '\n')
      .trim();
  };

  // 日志同步函数
  const syncLogToServer = async (newLogContent) => {
    try {
      // 计算新增的日志内容
      const diffContent = newLogContent.slice(lastLoggerValue.length);
      if (diffContent.trim()) {
        // 获取客户端ID
        const clientId = wsClient.getClientId();
        if (!clientId) {
          console.warn('无法获取客户端ID，跳过日志同步');
          return;
        }

        // 清理HTML标签，只发送纯文本
        const cleanContent = cleanHtmlContent(diffContent);
        if (!cleanContent.trim()) {
          // 如果清理后没有内容，跳过同步
          lastLoggerValue = newLogContent;
          return;
        }

        // 发送日志到服务端
        await wsClient.sendRequest('logger', 'syncLog', {
          content: cleanContent,
          timestamp: new Date().toISOString(),
          clientId: clientId
        });

        // 更新最后同步的内容
        lastLoggerValue = newLogContent;
      }
    } catch (error) {
      console.error('同步日志到服务端失败:', error);
    }
  };

  // 初始化WebSocket连接
  const initWebSocket = () => {
    // 使用降级机制，如果WebSocket连接失败，会在wsClient内部自动重试
    // getCurrentDocument().DocID;
    wsClient.connect().then(() => {
      // 每次重连后都重新获取一遍监控目录
      fetchWatchedDir()
    }).catch(error => {
      logger.value += `<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${error.message}</span><br/>`;
    });

    // Define the connection established handler
    const handleConnectionEstablished = () => {
      logger.value += `<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>`;

      // Resume in-progress tasks by their client-side task IDs
      const inProgressTaskIds = [];
      for (const taskId in map) {
        if (map.hasOwnProperty(taskId)) {
          const task = map[taskId];
          // A task is in-progress if its status is 0 (准备中) or 1 (进行中) and it's not marked as terminated locally.
          if ((task.status === 0 || task.status === 1) && !task.terminated) {
            if (!inProgressTaskIds.includes(taskId)) {
              inProgressTaskIds.push(taskId);
            }
          }
        }
      }

      if (inProgressTaskIds.length > 0) {
        let taskpaneVisible = false;
        try {
          const currentDoc = getCurrentDocument();
          if (currentDoc) {
            const docId = currentDoc.DocID;
            const tsIdKey = `taskpane_id_${docId}`;
            const tsId = window.Application.PluginStorage.getItem(tsIdKey);
            if (tsId) {
              const taskpane = window.Application.GetTaskPane(tsId);
              if (taskpane) {
                taskpaneVisible = taskpane.Visible;
              }
            }
          }
        } catch (e) {
          logger.value += `<span class="log-item warning">检查任务窗格可见性失败: ${e.message}</span><br/>`;
          // 默认为可见，以防检查失败导致功能阻塞
          taskpaneVisible = true;
        }

        // 使用 setTimeout 将弹窗逻辑延迟到下一个事件循环
        setTimeout(() => {
          if (taskpaneVisible) {
            // 替换window.confirm为自定义弹窗
            const confirmMessage = `检测到 ${inProgressTaskIds.length} 个未完成的任务，是否继续？`;

            // 显示自定义弹窗并处理结果
            showConfirm(confirmMessage).then(confirmed => {
              if (confirmed) {
                logger.value += `<span class="log-item info">用户选择继续 ${inProgressTaskIds.length} 个进行中的任务 (by taskId)...</span><br/>`;
                wsClient.sendRequest('urlMonitor', 'resumeUrlMonitors', { taskIds: inProgressTaskIds })
                  .then(response => {
                    if (response && response.success) {
                      logger.value += `<span class="log-item success">成功请求恢复任务。服务端响应: ${response.message || ''}</span><br/>`;
                    } else {
                      logger.value += `<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${response?.message || '未知错误'}</span><br/>`;
                    }
                  })
                  .catch(error => {
                    logger.value += `<span class="log-item error">请求恢复任务出错: ${error.message}</span><br/>`;
                  });
              } else {
                logger.value += `<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>`;
                inProgressTaskIds.forEach(taskId => {
                  stopTaskWithoutRemovingControl(taskId);
                });
                logger.value += `<span class="log-item success">${inProgressTaskIds.length} 个任务已停止（控件已保留）。</span><br/>`;
              }
            }).catch(error => {
              // 弹窗出现错误，默认停止任务但保留控件
              logger.value += `<span class="log-item error">弹窗错误: ${error.message}，默认停止任务（保留控件）</span><br/>`;
              inProgressTaskIds.forEach(taskId => {
                stopTaskWithoutRemovingControl(taskId);
              });
            });
          } else {
            logger.value += `<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>`;
            // 根据需求，如果任务窗格不可见，可以选择默认行为，例如自动终止或不执行任何操作。
            // 当前不执行任何操作，后续可以根据产品逻辑调整。
          }
        }, 0);
      }
    };

    // Set the handler for successful connection
    wsClient.setConnectionSuccessHandler(handleConnectionEstablished);

    // If WebSocket is already connected when this handler is set (e.g., App.vue connected first),
    // call the handler manually.
    if (wsClient.isConnected) {
      logger.value += `<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>`;
      handleConnectionEstablished();
    }

    // Listener for disconnection (this part can remain if needed for UI updates on disconnect)
    wsClient.addEventListener('connection', (data) => {
      if (data.status === 'disconnected') {
        logger.value += `<span class="log-item warning">WebSocket连接关闭，原因: ${data.reason || '未知'}, 代码: ${data.code || 'N/A'}，将自动重连</span><br/>`;
      }
    });

    // 添加事件监听
    wsClient.addEventListener('watcher', (data) => {
      wsMessages.push(data);

      // 更详细的日志
      if (data.type === 'watcher') {
        // logger.value += `<span class="log-item info">收到监控事件: ${data.eventType || data.action}</span><br/>`;
      }

      // 处理文件上传相关的WebSocket消息
      if (data.eventType === 'uploadSuccess') {
        // 文件名就是任务ID
        const filename = data.data?.file;
        const taskId = filename?.replace(/\.docx$/, '');
        // 生成一个唯一事件标识符，用于去重
        const eventId = `${data.eventType}_${filename}_${Date.now()}`;

        // 检查是否已处理过该事件，防止重复处理
        if (processedEvents.has(eventId)) {
          logger.value += `<span class="log-item warning">忽略重复的上传事件: ${filename}</span><br/>`;
          return;
        }

        // 标记为已处理
        processedEvents.add(eventId);

        // 限制已处理事件集合的大小，避免内存泄漏
        if (processedEvents.size > 100) {
          const iterator = processedEvents.values();
          processedEvents.delete(iterator.next().value);
        }

        // 从map中获取对应任务的wordType
        const wordType = taskId && map[taskId]?.wordType;
        handleWatcherEvent(data, wordType);
      }
      // 处理所有其他类型的监控事件
      else if (data.eventType) {
        // 统一处理所有事件类型，包括URL监控相关事件
        handleWatcherEvent(data, null);
      }
    });

    wsClient.addEventListener('urlMonitor', (data) => {
      wsMessages.push(data);

      // 更详细的日志
      if (data.type === 'urlMonitor') {
        logger.value += `<span class="log-item info">收到URL监控事件: ${data.eventType || data.action}</span><br/>`;
      }

      // 处理URL监控相关的消息
      if (data.eventType) {
        handleWatcherEvent(data, null);
      }
    });

    // 添加特殊处理health消息的监听器
    wsClient.addEventListener('health', (data) => {
      // 不将health消息添加到wsMessages中，避免占用内存
      // 更详细的日志，但仅在调试模式下显示
      if (import.meta.env.MODE === 'development') {
        logger.value += `<span class="log-item info">收到健康检查响应</span><br/>`;
      }
    });

    wsClient.addEventListener('error', (data) => {
      const errorMsg = data.error || '未知错误';
      logger.value += `<span class="log-item error">WebSocket错误: ${errorMsg}</span><br/>`;
      console.error('WebSocket错误:', data);
    });
  };

  // 添加用于URL监控的状态和方法
  const urlMonitorTasks = reactive({}); // 存储任务与URL监控的关联 {taskId: {urlId, url, isResultUrl}}

  /**
   * 开始监控URL并关联到任务
   * @param {string} url URL地址
   * @param {string} taskId 任务ID
   * @param {boolean} isResultUrl 是否为结果文件URL
   * @param {number} interval 检查间隔（毫秒）
   */
  const monitorUrlForTask = async (url, taskId, isResultUrl = false, interval = 5000, options = {}) => {
    try {
      logger.value += `<span class="log-item info">开始监控URL: ${url}</span><br/>`;

      const response = await wsClient.startUrlMonitoring(url, interval, {
        downloadOnSuccess: options.downloadOnSuccess !== undefined ? options.downloadOnSuccess : true,
        appKey: options.appKey,
        filename: options.filename,
        taskId
      })

      // logger.value += `<span class="log-item info">URL监控请求数据: ${JSON.stringify(response)}</span><br/>`;
      // 兼容WebSocket和HTTP返回的不同结构
      const success = response.success || response?.success;
      const urlId = response.urlId || response?.urlId;

      if (success && urlId) {
        // 存储URL监控与任务的关联
        urlMonitorTasks[taskId] = {
          urlId,
          url,
          isResultUrl,
          startTime: Date.now()
        };

        logger.value += `<span class="log-item success">URL监控已启动，ID: ${urlId}</span><br/>`;

        // 确认收到urlId后，告诉后端开始检查
        try {
          await wsClient.startUrlChecking(urlId);
        } catch (startError) {
          // 忽略启动检查的错误
        }

        return urlId;
      } else {
        throw new Error('服务器返回失败');
      }
    } catch (error) {
      logger.value += `<span class="log-item error">启动URL监控失败: ${error.message}</span><br/>`;
      return null;
    }
  };

  /**
   * 停止监控指定的URL
   * @param {string} urlId URL监控ID
   */
  const stopUrlMonitoring = async (urlId) => {
    if (!urlId) {
      logger.value += `<span class="log-item warning">无效的URL监控ID</span><br/>`;
      return false;
    }

    try {
      // 从本地列表中移除相关的任务记录（无论API调用是否成功）
      Object.keys(urlMonitorTasks).forEach(tid => {
        if (urlMonitorTasks[tid].urlId === urlId) {
          delete urlMonitorTasks[tid];
        }
      });

      // 发送停止监控的API请求
      logger.value += `<span class="log-item info">正在停止URL监控: ${urlId}</span><br/>`;

      const response = await requestWithFallback(
        async () => await wsClient.stopUrlMonitoring(urlId),
        'delete',
        `${getServerUrl()}/api/url/monitor/${urlId}`
      );

      if (response && (response.success || response?.success)) {
        logger.value += `<span class="log-item success">已停止URL监控: ${urlId}</span><br/>`;
        return true;
      } else {
        // 即使服务端返回失败，也认为本地已停止监控
        logger.value += `<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>`;
        return true;
      }
    } catch (error) {
      // 记录错误但仍然认为本地已停止监控
      logger.value += `<span class="log-item warning">停止URL监控API调用失败: ${error.message}，但已在客户端停止</span><br/>`;

      // 再次尝试发送请求（不等待响应）
      try {
        setTimeout(async () => {
          try {
            await requestWithFallback(
              async () => await wsClient.stopUrlMonitoring(urlId),
              'delete',
              `${getServerUrl()}/api/url/monitor/${urlId}`
            );
          } catch (e) {
            // 忽略错误
          }
        }, 1000);
      } catch { }

      return true; // 本地已清理，返回成功
    }
  };

  /**
   * 获取所有URL监控状态
   */
  const getUrlMonitorStatus = async () => {
    try {
      const response = await requestWithFallback(
        async () => await wsClient.getUrlMonitorStatus(),
        'get',
        `${getServerUrl()}/api/url/status`
      );
      return response.data || response;
    } catch (error) {
      logger.value += `<span class="log-item error">获取URL监控状态失败: ${error.message}</span><br/>`;
      return [];
    }
  };

  /**
   * 强制检查URL
   * @param {string} urlId URL监控ID
   */
  const forceUrlCheck = async (urlId) => {
    try {
      const response = await wsClient.forceUrlCheck(urlId)
      return response;
    } catch (error) {
      return false;
    }
  };
  // 处理文件监控服务的事件
  const handleWatcherEvent = async (event, wordType) => {
    if (event.eventType === 'uploadSuccess') {
      // 查找与此文件名匹配的任务
      const filename = event.data.file;
      const taskId = filename.replace(/\.docx$/, '');
      if (map[taskId]) {
        // 检查任务是否已终止
        if (map[taskId].terminated) {
          logger.value += `<span class="log-item info">忽略已终止任务 ${taskId.substring(0, 8)} 的上传通知</span><br/>`;
          return;
        }

        // 检查任务是否已经处理过上传成功事件
        if (map[taskId].uploadSuccess) {
          logger.value += `<span class="log-item warning">任务 ${taskId.substring(0, 8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;
          return;
        }

        logger.value += `<span class="log-item success">收到文件 ${filename} 上传成功通知</span><br/>`;
        logger.value += `<span class="log-item info">使用 wordType: ${wordType || map[taskId].wordType || 'wps-analysis'}</span><br/>`;

        // 更新任务状态
        map[taskId].status = 1; // 更新为上传成功状态
        map[taskId].uploadSuccess = true;

        // 继续处理API调用，优先使用传入的wordType，其次使用任务中存储的wordType，最后使用默认值
        await processTaskAfterUpload(taskId, wordType || map[taskId].wordType || 'wps-analysis');
      }
    }
    // 处理加密文件错误事件
    else if (event.eventType === 'encryptedFileError') {
      const filename = event.data.file;
      const taskId = filename.replace(/\.docx$/, '');

      if (map[taskId]) {
        logger.value += `<span class="log-item error">文件加密错误: ${filename}</span><br/>`;
        logger.value += `<span class="log-item error">${event.data.message}</span><br/>`;

        // 显示错误弹窗
        // showErrorDialog('文档加密错误', event.data.message, 'error');

        // 更新任务状态为错误
        map[taskId].status = -1;
        map[taskId].errorMessage = event.data.message || '文件已加密，无法处理';

        // 更新控件标题为异常状态
        try {
          const currentDocument = getCurrentDocument();
          if (currentDocument && currentDocument.ContentControls) {
            for (let i = 1; i <= currentDocument.ContentControls.Count; i++) {
              try {
                const control = currentDocument.ContentControls.Item(i);
                if (control && control.Title && (
                  control.Title === `任务_${taskId}` ||
                  control.Title === `任务增强_${taskId}` ||
                  control.Title === `校对_${taskId}`
                )) {
                  // 检查控件类型
                  const isEnhanced = control.Title === `任务增强_${taskId}` || map[taskId].isEnhanced;
                  const isCheckTask = control.Title === `校对_${taskId}` || map[taskId].isCheckTask;

                  // 更新控件标题为异常状态
                  if (isCheckTask) {
                    control.Title = `异常校对_${taskId}`;
                  } else if (isEnhanced) {
                    control.Title = `异常增强_${taskId}`;
                  } else {
                    control.Title = `异常_${taskId}`;
                  }

                  const taskType = isCheckTask ? '校对' : (isEnhanced ? '增强' : '普通');
                  logger.value += `<span class="log-item info">已将${taskType}任务${taskId.substring(0, 8)}控件标记为异常（文件加密）</span><br/>`;
                  break;
                }
              } catch (e) {
                continue; // 忽略单个控件错误
              }
            }
          }
        } catch (titleUpdateError) {
          logger.value += `<span class="log-item warning">更新控件标题失败: ${titleUpdateError.message}</span><br/>`;
        }

        // 尝试清除任务占位符
        tryRemoveTaskPlaceholder(taskId);
      }
    }
    // 处理文件上传错误事件
    else if (event.eventType === 'uploadError') {
      const filename = event.data.file;
      const taskId = filename.replace(/\.docx$/, '');

      if (map[taskId]) {
        logger.value += `<span class="log-item error">文件上传错误: ${filename}</span><br/>`;
        logger.value += `<span class="log-item error">${event.data.message}</span><br/>`;

        // 更新任务状态为错误
        map[taskId].status = -1;
        map[taskId].errorMessage = event.data.message || '文件上传失败';

        // 更新控件标题为异常状态
        try {
          const currentDocument = getCurrentDocument();
          if (currentDocument && currentDocument.ContentControls) {
            for (let i = 1; i <= currentDocument.ContentControls.Count; i++) {
              try {
                const control = currentDocument.ContentControls.Item(i);
                if (control && control.Title && (
                  control.Title === `任务_${taskId}` ||
                  control.Title === `任务增强_${taskId}` ||
                  control.Title === `校对_${taskId}`
                )) {
                  // 检查控件类型
                  const isEnhanced = control.Title === `任务增强_${taskId}` || map[taskId].isEnhanced;
                  const isCheckTask = control.Title === `校对_${taskId}` || map[taskId].isCheckTask;

                  // 更新控件标题为异常状态
                  if (isCheckTask) {
                    control.Title = `异常校对_${taskId}`;
                  } else if (isEnhanced) {
                    control.Title = `异常增强_${taskId}`;
                  } else {
                    control.Title = `异常_${taskId}`;
                  }

                  const taskType = isCheckTask ? '校对' : (isEnhanced ? '增强' : '普通');
                  logger.value += `<span class="log-item info">已将${taskType}任务${taskId.substring(0, 8)}控件标记为异常（文件上传失败）</span><br/>`;
                  break;
                }
              } catch (e) {
                continue; // 忽略单个控件错误
              }
            }
          }
        } catch (titleUpdateError) {
          logger.value += `<span class="log-item warning">更新控件标题失败: ${titleUpdateError.message}</span><br/>`;
        }

        // 尝试清除任务占位符
        tryRemoveTaskPlaceholder(taskId);
      }
    }
    // 处理URL监控事件
    else if (event.eventType === 'urlMonitorUpdate') {

    }
    else if (event.eventType === 'urlMonitorStopped') {
      const { urlId, url, taskId, downloadedPath } = event.data;
      // 清理关联的任务
      const associatedTaskIds = Object.keys(urlMonitorTasks).filter(tid => urlMonitorTasks[tid].urlId === urlId);
      if (associatedTaskIds.length > 0) {
        associatedTaskIds.forEach(tid => {
          // 更新任务的结果文件路径（如果有）
          if (downloadedPath && map[tid]) {
            map[tid].resultFile = downloadedPath;
            map[tid].resultDownloaded = true;
          }

          // 删除监控任务记录
          delete urlMonitorTasks[tid];;
        });
      }
    }
    // 处理文件下载成功事件
    else if (event.eventType === 'urlFileDownloaded') {
      const { urlId, url, filePath, taskId } = event.data;

      // 先检查urId是否仍在监控列表中
      const isUrlStillMonitored = Object.values(urlMonitorTasks).some(task => task.urlId === urlId);

      // 如果URL已不在监控列表中且任务已终止，忽略此事件
      if (!isUrlStillMonitored && taskId && map[taskId]?.terminated) {
        return;
      }

      // 检查任务是否已终止
      if (taskId && map[taskId] && map[taskId].terminated) {
        logger.value += `<span class="log-item info">忽略已终止任务 ${urlId} 的文件下载通知</span><br/>`;

        // 尝试再次停止URL监控
        if (urlId) {
          try {
            await wsClient.stopUrlMonitoring(urlId)
            if (urlMonitorTasks[taskId]) {
              delete urlMonitorTasks[taskId];
            }
          } catch { }
        }
        return;
      }

      if (taskId && map[taskId]) {
        logger.value += `<span class="log-item success">收到结果文件通知: ${filePath}</span><br/>`;

        // 检查是否为校对任务的JSON文件
        if (map[taskId].isCheckTask && filePath.endsWith('.wps.json')) {
          logger.value += `<span class="log-item info">处理校对任务JSON文件: ${filePath}</span><br/>`;

          try {
            // 使用WPS文件系统API读取本地JSON文件内容
            const fs = window.Application.FileSystem;
            const jsonContent = fs.ReadFile(filePath);
            const jsonData = JSON.parse(jsonContent);

            // 处理校对JSON数据并添加批注
            const processSuccess = await processCheckingJson(jsonData, taskId);

            if (processSuccess) {
              map[taskId].status = 2; // 标记为完成

              // 控件已被删除，不需要更新控件标题
              logger.value += `<span class="log-item success">校对任务${taskId.substring(0, 8)}已完成批注处理</span><br/>`;

              // 计算任务耗时
              const taskTime = getElapsedTime(map[taskId].startTime);
              logger.value += `<span class="log-item success">校对任务${taskId.substring(0, 8)}完成，总耗时${taskTime}</span><br/>`;
            }
          } catch (error) {
            logger.value += `<span class="log-item error">处理校对JSON文件失败: ${error.message}</span><br/>`;
            if (error.message.includes('Unsupported protocol')) {
              logger.value += `<span class="log-item error">文件路径格式错误，无法读取文件: ${filePath}</span><br/>`;
            }
            map[taskId].status = -1;
            map[taskId].errorMessage = `JSON处理失败: ${error.message}`;

            // 控件可能已被删除，不需要更新控件标题
            logger.value += `<span class="log-item error">校对任务${taskId.substring(0, 8)}处理失败</span><br/>`;
          }
        } else {
          // 非校对任务的常规处理
          // 更新任务状态
          map[taskId].resultFile = filePath;
          map[taskId].resultDownloaded = true;

          // 计算任务耗时
          const taskTime = getElapsedTime(map[taskId].startTime);
          logger.value += `<span class="log-item success">任务${taskId.substring(0, 8)}完成，总耗时${taskTime}</span><br/>`;

          // 手动触发更新结果
          await updateResult(taskId);
        }

        // 尝试停止URL监控
        if (urlMonitorTasks[taskId] && urlMonitorTasks[taskId].urlId) {
          stopUrlMonitoring(urlMonitorTasks[taskId].urlId)
          logger.value += `<span class="log-item info">已停止URL监控</span><br/>`;

          delete urlMonitorTasks[taskId];
        }
      } else if (urlId) {
        // 没有关联的任务，但有下载
        logger.value += `<span class="log-item info">URL文件已下载: ${filePath}</span><br/>`;

        // 在urlMonitorTasks中查找可能对应的任务，但排除已终止的任务
        const possibleTaskIds = Object.keys(urlMonitorTasks).filter(tid =>
          urlMonitorTasks[tid].urlId === urlId && !map[tid]?.terminated
        );

        if (possibleTaskIds.length > 0) {
          possibleTaskIds.forEach(async (tid) => {
            if (map[tid]) {
              logger.value += `<span class="log-item info">关联到任务: ${tid.substring(0, 8)}</span><br/>`;
              map[tid].resultFile = filePath;
              map[tid].resultDownloaded = true;

              // 检查是否为校对任务的JSON文件
              if (map[tid].isCheckTask && filePath.endsWith('.wps.json')) {
                try {
                  // 使用WPS文件系统API读取本地JSON文件内容
                  const fs = window.Application.FileSystem;
                  const jsonContent = fs.ReadFile(filePath);
                  const jsonData = JSON.parse(jsonContent);

                  const processSuccess = await processCheckingJson(jsonData, tid);

                  if (processSuccess) {
                    // 只有在任务是处理中状态时才更新为完成
                    if (map[tid].status === 1) {
                      map[tid].status = 2; // 更新为完成状态

                      // 控件已被删除，不需要更新控件标题
                      logger.value += `<span class="log-item info">校对控件已删除，任务状态已更新为完成</span><br/>`;

                      // 计算任务耗时
                      const taskTime = getElapsedTime(map[tid].startTime);
                      logger.value += `<span class="log-item success">校对任务${tid.substring(0, 8)}完成，总耗时${taskTime}</span><br/>`;
                    }
                  }
                } catch (error) {
                  logger.value += `<span class="log-item error">处理校对JSON失败: ${error.message}</span><br/>`;
                  // 更新校对任务状态为异常
                  if (map[tid].status === 1) {
                    map[tid].status = -1;
                    map[tid].errorMessage = `JSON处理失败: ${error.message}`;

                    // 控件可能已被删除，不需要更新控件标题
                    logger.value += `<span class="log-item error">校对任务${tid.substring(0, 8)}处理失败</span><br/>`;
                  }
                }
              } else {
                // 非校对任务的常规处理
                // 只有在任务是处理中状态时才更新为完成
                if (map[tid].status === 1) {
                  map[tid].status = 2; // 更新为完成状态

                  // 计算任务耗时
                  const taskTime = getElapsedTime(map[tid].startTime);
                  logger.value += `<span class="log-item success">任务${tid.substring(0, 8)}完成，总耗时${taskTime}</span><br/>`;
                }
              }
            }
          });
        }
      }
    }
    // 处理文件下载失败事件
    else if (event.eventType === 'urlFileDownloadError') {
      const { urlId, url, error, taskId } = event.data;

      // 检查任务是否已终止
      if (taskId && map[taskId] && map[taskId].terminated) {
        logger.value += `<span class="log-item info">忽略已终止任务 ${taskId.substring(0, 8)} 的下载失败通知</span><br/>`;
        return;
      }

      logger.value += `<span class="log-item error">下载URL文件失败: ${error}</span><br/>`;

      if (taskId && map[taskId]) {
        // 更新任务状态为错误
        map[taskId].status = -1;
        map[taskId].errorMessage = `下载失败: ${error}`;

        // 更新控件标题为异常状态
        try {
          const currentDocument = getCurrentDocument();
          if (currentDocument && currentDocument.ContentControls) {
            for (let i = 1; i <= currentDocument.ContentControls.Count; i++) {
              try {
                const control = currentDocument.ContentControls.Item(i);
                if (control && control.Title && (
                  control.Title === `任务_${taskId}` ||
                  control.Title === `任务增强_${taskId}` ||
                  control.Title === `校对_${taskId}`
                )) {
                  // 检查控件类型
                  const isEnhanced = control.Title === `任务增强_${taskId}` || map[taskId].isEnhanced;
                  const isCheckTask = control.Title === `校对_${taskId}` || map[taskId].isCheckTask;

                  // 更新控件标题为异常状态
                  if (isCheckTask) {
                    control.Title = `异常校对_${taskId}`;
                  } else if (isEnhanced) {
                    control.Title = `异常增强_${taskId}`;
                  } else {
                    control.Title = `异常_${taskId}`;
                  }

                  const taskType = isCheckTask ? '校对' : (isEnhanced ? '增强' : '普通');
                  logger.value += `<span class="log-item info">已将${taskType}任务${taskId.substring(0, 8)}控件标记为异常</span><br/>`;
                  break;
                }
              } catch (e) {
                continue; // 忽略单个控件错误
              }
            }
          }
        } catch (titleUpdateError) {
          logger.value += `<span class="log-item warning">更新控件标题失败: ${titleUpdateError.message}</span><br/>`;
        }

        // 尝试清除任务占位符
        tryRemoveTaskPlaceholder(taskId);

        // 从监控列表中移除
        if (urlMonitorTasks[taskId]) {
          delete urlMonitorTasks[taskId];
        }
      }

      // 尝试停止URL监控
      if (urlId) {
        try {
          logger.value += `<span class="log-item info">尝试停止URL监控: ${urlId}</span><br/>`;
          await requestWithFallback(
            async () => await wsClient.stopUrlMonitoring(urlId),
            'delete',
            `${getServerUrl()}/api/url/monitor/${urlId}`
          );
        } catch { }
      }
    }
    else if (event.eventType === 'resumeUrlMonitors') {
      console.log(event.data)
    }
  };

  // 修改processTaskAfterUpload函数使用保存的appKey
  const processTaskAfterUpload = async (uuid, wordType = 'wps-analysis') => {
    try {
      if (!appKeyInfo.appKey) {
        throw new Error('未初始化appKey信息');
      }

      const access_token = await getAccessToken();
      const ossUrl = `https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${uuid}.docx`;
      const response = await axios.post(getApiBaseUrl() + '/api/open/ticket/v1/ai_comment/create', {
        access_token,
        data: {
          app_key: appKeyInfo.appKey,
          subject: subject.value,
          stage: stage.value,
          file_name: `${uuid}`,
          word_url: ossUrl,
          word_type: wordType,
          is_ai_auto: true,
          is_ai_edit: true,
          create_user_id: appKeyInfo.userId,
          create_username: appKeyInfo.userName,
          callback_data: {
            callback_url: "http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"
          }
        }
      });

      // logger.value += `<span class="log-item success">API调用成功: ${uuid}</span><br/>`;

      const ticketId = response.data.data.ticket_id;
      if (!ticketId) {
        // logger.value += `<span class="log-item error">无法获取ticket_id</span><br/>`;
        if (map[uuid]) {
          map[uuid].status = -1;
          map[uuid].errorMessage = '无法获取ticket_id';
          tryRemoveTaskPlaceholder(uuid); // 删除文档中的占位符
        }
        return false;
      }

      logger.value += `<span class="log-item info">获取到ticket_id: ${ticketId}，开始监控结果文件</span><br/>`;

      // 根据wordType决定监控的URL和文件名
      let resultUrl, filename;
      if (wordType === 'wps-check') {
        // 校对任务监控aiEditJsonUrl
        resultUrl = `https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${appKeyInfo.appKey}/ai/${ticketId}.wps.json`;
        filename = `${ticketId}.wps.json`;
        logger.value += `<span class="log-item info">校对任务，监控JSON文件: ${filename}</span><br/>`;
      } else {
        // 普通解析任务监控docx文件
        resultUrl = `https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${ticketId}.wps.docx`;
        filename = `${ticketId}.wps.docx`;
        logger.value += `<span class="log-item info">解析任务，监控DOCX文件: ${filename}</span><br/>`;
      }

      // 准备监控选项
      const monitorOptions = {
        downloadOnSuccess: true,
        filename: filename,
        appKey: appKeyInfo.appKey,
        ticketId: ticketId,
        taskId: uuid
      };

      // 开始监控结果文件URL
      const urlId = await monitorUrlForTask(resultUrl, uuid, true, 3000, monitorOptions);

      // 更新任务状态
      if (map[uuid]) {
        map[uuid].ticketId = ticketId;
        map[uuid].resultUrl = resultUrl;
        map[uuid].urlMonitorId = urlId;
        map[uuid].status = 1; // 处理中
      }

      return response.data;
    } catch (error) {
      logger.value += `<span class="log-item error">API调用失败: ${error.message}</span><br/>`;
      if (map[uuid]) {
        map[uuid].status = -1;
        map[uuid].errorMessage = `API调用失败: ${error.message}`;
        tryRemoveTaskPlaceholder(uuid); // 删除文档中的占位符
      }
      return false;
    }
  };

  const uploadoss = async (uuid, wordType = 'wps-analysis') => {
    return new Promise((resolve, reject) => {
      try {
        // 记录使用的监控目录，用于日志显示
        logger.value += `<span class="log-item info">监控目录: ${watchedDir.value}</span><br/>`;
        logger.value += `<span class="log-item info">使用文档类型: ${wordType}</span><br/>`;
        // 更新任务状态
        if (map[uuid]) {
          map[uuid].status = 0; // 上传中
          map[uuid].wordType = wordType; // 确保任务中存储了wordType
          map[uuid].uploadSuccess = false; // 初始化上传状态
        }

        // 不再需要轮询检查文件上传状态，等待WebSocket通知
        logger.value += `<span class="log-item info">正在等待文件上传完成的通知...</span><br/>`;

        // 使用轮询检查任务状态，替代事件监听方式
        const checkInterval = 1000; // 1秒检查一次
        const maxChecks = 10; // 最多检查30次，即30秒
        let checkCount = 0;

        const statusChecker = setInterval(() => {
          // 增加检查计数
          checkCount++;

          // 检查是否已上传成功
          if (map[uuid] && map[uuid].uploadSuccess) {
            clearInterval(statusChecker);
            resolve(true);
            return;
          }

          // 检查任务是否已经异常
          if (map[uuid] && map[uuid].status === -1) {
            clearInterval(statusChecker);
            logger.value += `<span class="log-item error">任务${uuid.substring(0, 8)}已异常，停止等待上传完成</span><br/>`;
            reject(new Error(map[uuid].errorMessage || '任务已异常'));
          }

          // 检查是否超时
          if (checkCount >= maxChecks) {
            clearInterval(statusChecker);
            logger.value += `<span class="log-item warning">等待上传完成超时，请检查文件状态</span><br/>`;
            // reject(new Error('上传超时，未收到完成通知'));
            tryRemoveTaskPlaceholder(uuid);
            return;
          }
        }, checkInterval);

        // 返回初始成功，实际上会等待状态检查完成
      } catch (error) {
        logger.value += `<span class="log-item error">任务处理异常: ${error.message}</span><br/>`;
        if (map[uuid]) {
          map[uuid].status = -1;
          map[uuid].errorMessage = `任务处理异常: ${error.message}`;
        }
        tryRemoveTaskPlaceholder(uuid);
        reject(error);
      }
    });
  };

  const handle1 = async () => {
    logger.value += '<span class="log-item info">扫描文档中已有的任务...</span><br/>'
    const wps = window.wps;
    const currentDocument = wps.ActiveDocument;
    docId.value = currentDocument.DocID;
    windowId.value = wps.ActiveWindow.Index;

    if (currentDocument?.ContentControls) {
      // 遍历所有内容控件
      for (let i = 1; i <= currentDocument.ContentControls.Count; i++) {
        const control = currentDocument.ContentControls.Item(i);
        if (control && control.Title) {
          let tid = null;
          let status = 1; // 默认为进行中状态
          let isEnhanced = false; // 初始化增强标识

          // 根据控件标题确定任务ID和状态
          let isCheckTask = false; // 标记是否为校对任务
          if (control.Title.startsWith('任务增强_')) {
            tid = control.Title.substring(5); // 去掉"任务增强_"前缀
            status = 1; // 进行中
            isEnhanced = true; // 设置增强标识
          } else if (control.Title.startsWith('任务_')) {
            tid = control.Title.substring(3); // 去掉"任务_"前缀
            status = 1; // 进行中
          } else if (control.Title.startsWith('校对_')) {
            tid = control.Title.substring(3); // 去掉"校对_"前缀
            status = 1; // 进行中
            isCheckTask = true; // 标记为校对任务
          } else if (control.Title.startsWith('已完成增强_')) {
            tid = control.Title.substring(6); // 去掉"已完成增强_"前缀
            status = 2; // 已完成
            isEnhanced = true; // 设置增强标识
          } else if (control.Title.startsWith('已完成校对_')) {
            tid = control.Title.substring(6); // 去掉"已完成校对_"前缀
            status = 2; // 已完成
            isCheckTask = true; // 标记为校对任务
          } else if (control.Title.startsWith('已完成_')) {
            tid = control.Title.substring(4); // 去掉"已完成_"前缀
            status = 2; // 已完成
          } else if (control.Title.startsWith('异常增强_')) {
            tid = control.Title.substring(5); // 去掉"异常增强_"前缀
            status = -1; // 异常
            isEnhanced = true; // 设置增强标识
          } else if (control.Title.startsWith('异常校对_')) {
            tid = control.Title.substring(5); // 去掉"异常校对_"前缀
            status = -1; // 异常
            isCheckTask = true; // 标记为校对任务
          } else if (control.Title.startsWith('异常_')) {
            tid = control.Title.substring(3); // 去掉"异常_"前缀
            status = -1; // 异常
          } else if (control.Title.startsWith('已停止增强_')) {
            tid = control.Title.substring(6); // 去掉"已停止增强_"前缀
            status = 4; // 已停止
            isEnhanced = true; // 设置增强标识
          } else if (control.Title.startsWith('已停止校对_')) {
            tid = control.Title.substring(6); // 去掉"已停止校对_"前缀
            status = 4; // 已停止
            isCheckTask = true; // 标记为校对任务
          } else if (control.Title.startsWith('已停止_')) {
            tid = control.Title.substring(4); // 去掉"已停止_"前缀
            status = 4; // 已停止
          }

          // 如果识别出了任务ID，则添加到map中
          if (tid && !map[tid]) {
            // 尝试获取控件中的文本内容作为题目
            let controlText = '';
            try {
              controlText = control.Range?.Text || '';
            } catch (e) {
              // 忽略获取文本失败的错误
            }

            // 从任务ID中提取时间戳信息，如果失败则使用比当前时间更早的时间
            // 这样确保已有任务总是排在新任务后面
            let taskStartTime = Date.now() - (24 * 60 * 60 * 1000); // 默认使用24小时前的时间戳
            try {
              // 如果任务ID是MongoDB ObjectId格式，前8位是时间戳（秒）
              if (tid.length === 24) {
                const timestampHex = tid.substring(0, 8);
                const timestamp = parseInt(timestampHex, 16);
                if (!isNaN(timestamp) && timestamp > 0 && timestamp < 2147483647) { // 确保时间戳合理（小于2038年）
                  taskStartTime = timestamp * 1000; // 转换为毫秒
                }
              } else {
                // 对于其他格式的任务ID，根据任务状态调整时间
                // 越旧的状态，时间戳越早
                const baseTime = Date.now() - (24 * 60 * 60 * 1000); // 24小时前
                if (status === 2) { // 已完成
                  taskStartTime = baseTime - (60 * 60 * 1000); // 25小时前
                } else if (status === -1) { // 异常
                  taskStartTime = baseTime - (30 * 60 * 1000); // 24.5小时前
                } else if (status === 4) { // 已停止
                  taskStartTime = baseTime - (45 * 60 * 1000); // 24.75小时前
                } else { // 进行中或其他状态
                  taskStartTime = baseTime; // 24小时前
                }
              }
            } catch (e) {
              // 如果解析失败，保持使用24小时前的时间
            }

            map[tid] = {
              status: status,
              startTime: taskStartTime,
              contentControlId: control.ID,
              isEnhanced: isEnhanced, // 保存增强标识
              isCheckTask: isCheckTask, // 保存校对任务标识
              selectedText: controlText // 保存控件中的文本内容
            };
            const statusText = status === 1 ? '进行中' : (status === 2 ? '已完成' : (status === -1 ? '异常' : (status === 4 ? '已停止' : '未知')));
            const taskType = isCheckTask ? '校对' : (isEnhanced ? '增强' : '普通');
            logger.value += `<span class="log-item info">发现已有${taskType}任务: ${tid.substring(0, 8)}, 状态: ${statusText}</span><br/>`;
          }
        }
      }
    }
  };

  const tryRemoveTaskPlaceholder = async (tid, deleteControl = false) => {
    // 由于不再使用文本占位符，此函数可简化为仅解锁和删除内容控件
    try {
      if (!map[tid]) {
        logger.value += `<span class="log-item warning">找不到任务${tid.substring(0, 8)}的记录，无法清除控件</span><br/>`;
        return;
      }

      // 如果是主动删除控件，将状态设置为已释放
      if (deleteControl && map[tid].status === 1) {
        map[tid].status = 3; // 3 for "已释放" (Released)
        map[tid].errorMessage = '用户主动释放';
      }

      const currentDocument = getCurrentDocument();

      if (!currentDocument) {
        logger.value += `<span class="log-item error">无法获取当前文档</span><br/>`;
        return;
      }

      let controlRemoved = false;
      // 获取任务是否为增强类型
      const isEnhanced = map[tid].isEnhanced;

      await withVBAOperationWrapper('删除内容控件', async () => {
        // 多种方式尝试找到并删除控件
        // 方式1：通过Title查找
        if (currentDocument.ContentControls && currentDocument.ContentControls.Count > 0) {
          for (let i = currentDocument.ContentControls.Count; i >= 1; i--) {
            try {
              const control = currentDocument.ContentControls.Item(i);
              if (control && control.Title && control.Title.includes(tid)) {
                control.LockContents = false;
                control.Delete(false);
                controlRemoved = true;
                console.log(control.Title)
                logger.value += `<span class="log-item success">已解锁并删除${isEnhanced ? '增强' : '普通'}任务${tid.substring(0, 8)}的内容控件</span><br/>`;
              }
            } catch (controlError) {
              // 忽略单个控件的访问错误，继续处理下一个
              logger.value += `<span class="log-item warning">访问第${i}个控件时出错: ${controlError.message}</span><br/>`;
              continue;
            }
          }
        }
      });

      if (controlRemoved) {
        map[tid].placeholderRemoved = true;
      } else {
        logger.value += `<span class="log-item warning">未能找到或删除${isEnhanced ? '增强' : '普通'}任务${tid.substring(0, 8)}的内容控件</span><br/>`;
      }
    } catch (error) {
      logger.value += `<span class="log-item error">删除内容控件失败: ${error.message}</span><br/>`;
    }
  };
  // 添加跳转到任务控件的函数
  const navigateToTaskControl = (tid) => {
    try {
      const wps = window.wps;
      const document = getCurrentDocument();

      if (!document || !document.ContentControls) {
        logger.value += `<span class="log-item error">无法访问文档或内容控件</span><br/>`;
        return;
      }

      // 使用改进的控件查找函数
      const contentControl = findTaskContentControl(tid, document);

      if (!contentControl) {
        // 检查任务是否已完成，完成的任务的控件可能已被删除
        const task = map[tid];
        if (task && (task.status === 2 || task.status === -1)) {
          logger.value += `<span class="log-item info">任务ID: ${tid.substring(0, 8)} 已完成，内容控件已不存在</span><br/>`;
        } else {
          logger.value += `<span class="log-item error">找不到任务ID: ${tid.substring(0, 8)} 对应的内容控件</span><br/>`;
        }
        return;
      }

      // 选中并跳转到内容控件位置
      contentControl.Range.Select();
      // 获取当前活动窗口
      const activeWindow = wps.Windows.Item(windowId.value);
      if (activeWindow) {
        // 尝试使用ScrollIntoView方法将选中内容滚动到视图中央
        if (activeWindow.Selection && activeWindow.Selection.Range) {
          // activeWindow.Selection.Range.ScrollIntoView();
          activeWindow.ScrollIntoView(activeWindow.Selection.Range, true);
        }
      }

      const isEnhanced = map[tid]?.isEnhanced;
      logger.value += `<span class="log-item success">已跳转到${isEnhanced ? '增强' : '普通'}任务ID: ${tid.substring(0, 8)} 位置</span><br/>`;
    } catch (error) {
      logger.value += `<span class="log-item error">跳转到任务控件失败: ${error.message}</span><br/>`;
    }
  };

  // 添加控件查找的辅助函数
  const findTaskContentControl = (tid, currentDocument) => {
    if (!currentDocument || !currentDocument.ContentControls) {
      return null;
    }

    const taskInfo = map[tid];
    const isEnhanced = taskInfo?.isEnhanced;
    const isCheckTask = taskInfo?.isCheckTask;

    // 构建可能的控件标题列表（按优先级排序）
    const possibleTitles = [
      `任务_${tid}`,
      `任务增强_${tid}`,
      `校对_${tid}`,
      `已完成_${tid}`,
      `已完成增强_${tid}`,
      `已完成校对_${tid}`,
      `异常_${tid}`,
      `异常增强_${tid}`,
      `异常校对_${tid}`,
      `已停止_${tid}`,
      `已停止增强_${tid}`,
      `已停止校对_${tid}`
    ];

    // 根据任务类型调整搜索优先级
    if (isCheckTask) {
      possibleTitles.unshift(`校对_${tid}`);
    } else if (isEnhanced) {
      possibleTitles.unshift(`任务增强_${tid}`);
    } else {
      possibleTitles.unshift(`任务_${tid}`);
    }

    let foundControl = null;
    let controlIndex = -1;

    // 遍历所有控件，使用精确匹配
    for (let i = 1; i <= currentDocument.ContentControls.Count; i++) {
      try {
        const control = currentDocument.ContentControls.Item(i);
        if (!control || !control.Title) {
          continue;
        }

        // 精确匹配控件标题
        const titleIndex = possibleTitles.indexOf(control.Title);
        if (titleIndex !== -1) {
          // 如果找到更高优先级的匹配，或者是第一次找到匹配
          if (foundControl === null || titleIndex < controlIndex) {
            foundControl = control;
            controlIndex = titleIndex;
          }
        }
      } catch (controlError) {
        // 记录控件访问错误但继续查找
        logger.value += `<span class="log-item warning">访问第${i}个控件时出错: ${controlError.message}</span><br/>`;
        continue;
      }
    }

    return foundControl;
  };

  const updateResult = async (tid) => {
    const document = getCurrentDocument();
    const item = map[tid]
    if (!item) {
      logger.value += `<span class="log-item error">找不到ID为${tid.substring(0, 8)}的任务, 现有任务ID: ${Object.keys(map).join(', ')}</span><br/>`
      return
    }

    // 检查任务是否已被手动终止
    if (item.terminated) return

    // 确保只有进行中的任务才能插入文档
    if (item.status !== 1) {
      return;
    }

    // 检查任务是否已经插入过文档，避免重复插入
    if (item.documentInserted) {
      logger.value += `<span class="log-item info">任务${tid.substring(0, 8)}已插入过文档，跳过重复插入</span><br/>`;
      return;
    }

    // 使用改进的控件查找函数
    const currentDocument = getCurrentDocument();
    let contentControl = findTaskContentControl(tid, currentDocument);

    if (!contentControl) {
      // 增加重试计数
      if (!item.controlSearchRetryCount) {
        item.controlSearchRetryCount = 0;
      }
      item.controlSearchRetryCount++;

      logger.value += `<span class="log-item warning">找不到任务${tid.substring(0, 8)}的内容控件 (第${item.controlSearchRetryCount}次尝试)</span><br/>`;

      // 添加详细的调试信息
      if (currentDocument && currentDocument.ContentControls) {
        const allTitles = [];
        const matchingTitles = [];
        for (let i = 1; i <= Math.min(currentDocument.ContentControls.Count, 20); i++) {
          try {
            const control = currentDocument.ContentControls.Item(i);
            if (control && control.Title) {
              allTitles.push(control.Title);
              // 检查是否包含任务ID
              if (control.Title.includes(tid.substring(0, 8))) {
                matchingTitles.push(control.Title);
              }
            }
          } catch (e) {
            allTitles.push(`[访问错误:${e.message}]`);
          }
        }
        logger.value += `<span class="log-item info">当前文档前20个控件标题: ${allTitles.join(', ')}</span><br/>`;
        logger.value += `<span class="log-item info">总控件数量: ${currentDocument.ContentControls.Count}</span><br/>`;
        if (matchingTitles.length > 0) {
          logger.value += `<span class="log-item info">包含任务ID的控件: ${matchingTitles.join(', ')}</span><br/>`;
        }
      }

      // 如果重试次数超过3次，标记任务为失败
      if (item.controlSearchRetryCount >= 3) {
        map[tid].status = -1;
        map[tid].errorMessage = '无法找到对应的内容控件';
        logger.value += `<span class="log-item error">任务${tid.substring(0, 8)}重试3次后仍无法找到控件，标记为失败</span><br/>`;
        return;
      }

      // 检查任务是否已被标记为错误，但状态可能尚未更新
      if (item.errorMessage && item.status === 1) {
        map[tid].status = -1;
        logger.value += `<span class="log-item error">任务${tid.substring(0, 8)}失败: ${item.errorMessage}</span><br/>`;
        return;
      }

      // 等待下次重试
      return;
    }

    // 找到控件后重置重试计数
    if (item.controlSearchRetryCount) {
      logger.value += `<span class="log-item success">任务${tid.substring(0, 8)}在第${item.controlSearchRetryCount + 1}次尝试后找到控件</span><br/>`;
      delete item.controlSearchRetryCount;
    }

    // 检查任务是否已被标记为错误，但状态可能尚未更新
    if (item.errorMessage && item.status === 1) {
      map[tid].status = -1;
      logger.value += `<span class="log-item error">任务${tid.substring(0, 8)}失败: ${item.errorMessage}</span><br/>`;
      tryRemoveTaskPlaceholder(tid);
      return;
    }

    // 检查是否有结果文件
    if (!item.resultFile) {
      return;
    }

    // 标记任务即将插入文档，防止重复插入
    map[tid].documentInserted = true;

    // 获取内容控件的范围
    const controlRange = contentControl.Range;

    // 首先解锁内容控件
    contentControl.LockContents = false;

    // 在内容控件后插入结果文件
    try {
      await withVBAOperationWrapper('插入结果文件', async () => {
        // 自动插入文件
        logger.value += `<span class="log-item info">正在自动插入结果文件${item.resultFile}...</span><br/>`
        // 在内容控件后创建插入点
        const insertionRange = document.Range(controlRange.End, controlRange.End);
        // 先插入换行，确保内容在新行显示
        insertionRange.InsertParagraphAfter();
        // 兼容 垂直制表符 的情况
        // insertionRange.InsertAfter('\r');
        if (controlRange.Text.includes('\u000b')) {
          insertionRange.InsertAfter('\u000b');
        }
        // 更新插入点位置到新段落
        let end = insertionRange.End;
        const tableCount = controlRange.Tables?.Count;
        if (tableCount) {
          const lastTable = controlRange.Tables.Item(tableCount)
          if (lastTable?.Range?.End > end) {
            lastTable.Range.InsertParagraphAfter();
            end = lastTable?.Range?.End
          }
        }
        let newInsertionRange = document.Range(end, end);
        // 使用insertFile方法插入DOCX文件
        newInsertionRange.InsertFile(item.resultFile);

        // 重新查找控件，因为插入文件后控件可能发生变化
        const updatedContentControl = findTaskContentControl(tid, currentDocument);
        if (updatedContentControl) {
          // 如果找到了更新的控件，使用它
          contentControl = updatedContentControl;
        }
        // 重新获取文档的最新范围，因为插入文件后文档长度发生了变化
        const endRange = document.Range(contentControl.Range.End - 1, contentControl.Range.End);
        const endText = endRange.Text;
        // 检查是否有连续的换行符并删除多余的
        if (endText.includes('\r')) {
          endRange.Delete()
        }
      });

      // 更新任务状态为已完成
      map[tid].status = 2;

      // 发送WebSocket事件通知服务端更新数据库任务状态
      if (urlMonitorTasks[tid] && urlMonitorTasks[tid].urlId) {
        wsClient.sendRequest('urlMonitor', 'updateTaskStatus', {
          urlId: urlMonitorTasks[tid].urlId,
          status: 'completed',
          taskId: tid
        }).then(response => {
          logger.value += `<span class="log-item info">已通知服务端更新任务${tid.substring(0, 8)}状态为完成</span><br/>`;
        }).catch(error => {
          logger.value += `<span class="log-item warning">通知服务端更新任务状态失败: ${error.message}</span><br/>`;
        });
      }

      // 任务完成时显示总耗时
      const totalTime = getElapsedTime(item.startTime);
      logger.value += `<span class="log-item success">任务${tid.substring(0, 8)}处理完成，总耗时${totalTime}</span><br/>`;

      // 重命名内容控件以表示任务已完成
      // 检查控件类型
      const isEnhanced = contentControl.Title === `任务增强_${tid}` || item.isEnhanced;
      const isCheckTask = contentControl.Title === `校对_${tid}` || item.isCheckTask;
      // 先找到内容控件
      if (contentControl) {
        // 根据任务类型设置不同的标题
        if (isCheckTask) {
          contentControl.Title = `已完成校对_${tid}`;
        } else if (isEnhanced) {
          contentControl.Title = `已完成增强_${tid}`;
        } else {
          contentControl.Title = `已完成_${tid}`;
        }

        const taskType = isCheckTask ? '校对' : (isEnhanced ? '增强' : '普通');
        logger.value += `<span class="log-item info">已将${taskType}任务${tid.substring(0, 8)}控件标记为已完成</span><br/>`;
      }
    } catch (error) {
      // 插入失败时，重置documentInserted标记，允许重试
      map[tid].documentInserted = false;
      map[tid].status = -1; // 标记为异常

      // 重命名内容控件以表示任务出错
      // 检查控件类型
      const isEnhanced = contentControl.Title === `任务增强_${tid}` || item.isEnhanced;
      const isCheckTask = contentControl.Title === `校对_${tid}` || item.isCheckTask;
      if (contentControl) {
        // 根据任务类型设置不同的标题
        if (isCheckTask) {
          contentControl.Title = `异常校对_${tid}`;
        } else if (isEnhanced) {
          contentControl.Title = `异常增强_${tid}`;
        } else {
          contentControl.Title = `异常_${tid}`;
        }

        const taskType = isCheckTask ? '校对' : (isEnhanced ? '增强' : '普通');
        logger.value += `<span class="log-item info">已将${taskType}任务${tid.substring(0, 8)}控件标记为异常</span><br/>`;
      }
      logger.value += `<span class="log-item error">插入文档失败: ${error.message}</span><br/>`;
    }
  };

  // 添加并发控制变量
  const processingTasks = new Set();
  const maxConcurrentTasks = 3; // 最大同时处理的任务数

  const runschedule = async () => {
    const sleep = (t = 1000) => {
      return new Promise(resolve => {
        setTimeout(() => resolve(), t)
      })
    }

    // 添加任务开始时间记录
    const taskStartTimes = new Map();

    // 改进的任务处理函数，添加并发控制
    const processTaskWithConcurrencyControl = async (tid) => {
      if (processingTasks.has(tid)) {
        // 任务已在处理中，跳过
        return;
      }

      if (processingTasks.size >= maxConcurrentTasks) {
        // 达到最大并发数，跳过
        return;
      }

      processingTasks.add(tid);
      try {
        await updateResult(tid);
      } catch (error) {
        logger.value += `<span class="log-item error">处理任务${tid.substring(0, 8)}时出错: ${error.message}</span><br/>`;
      } finally {
        processingTasks.delete(tid);
      }
    };

    // 第一次立即执行
    const tasks = Object.keys(map).filter(k => map[k].status === 1 && !map[k].terminated)
    if (!tasks.length) {
      logger.value += '<span class="log-item info">目前没有解析中的任务...</span><br/>'
    } else {
      // 记录任务开始时间
      tasks.forEach(tid => {
        if (!taskStartTimes.has(tid)) {
          taskStartTimes.set(tid, Date.now());
        }
      });

      // 使用并发控制处理任务
      const taskPromises = tasks.slice(0, maxConcurrentTasks).map(tid => processTaskWithConcurrencyControl(tid));
      await Promise.all(taskPromises);
    }

    // 后续循环检查
    while (true) {
      await sleep(3000)
      const tasks = Object.keys(map).filter(k => map[k].status === 1 && !map[k].terminated)

      // 检查任务是否超时
      tasks.forEach(tid => {
        if (!taskStartTimes.has(tid)) {
          taskStartTimes.set(tid, Date.now());
        }

        const startTime = taskStartTimes.get(tid);
        const currentTime = Date.now();
        const timeElapsed = (currentTime - startTime) / 1000 / 60; // 转换为分钟

        // 如果超过5分钟
        if (timeElapsed >= 50000) {
          // 自动终止任务
          if (map[tid] && !map[tid].terminated) {
            map[tid].terminated = true;
            map[tid].status = -1; // 设置为终止状态
            logger.value += `<span class="log-item warning">任务 ${tid.substring(0, 8)} 执行超过5分钟，已自动终止</span><br/>`;
            // 清理开始时间记录
            taskStartTimes.delete(tid);
            // 从处理中任务集合中移除
            processingTasks.delete(tid);
          }
        }
      });

      if (tasks.length) {
        // 只处理未在处理中的任务，并限制并发数
        const availableTasks = tasks.filter(tid => !processingTasks.has(tid));
        const tasksToProcess = availableTasks.slice(0, maxConcurrentTasks - processingTasks.size);

        if (tasksToProcess.length > 0) {
          const taskPromises = tasksToProcess.map(tid => processTaskWithConcurrencyControl(tid));
          await Promise.all(taskPromises);
        }
      }
    }
  };

  const run1 = async (wordType = 'wps-analysis', onFileSavedCallback = null) => {
    const selectionInfo = isSelectionInTaskControl();
    if (selectionInfo) {
      const { primary, all } = selectionInfo;
      const { taskId, control, task, isEnhanced, overlapType } = primary;

      // 检查是否有任何重叠的控件对应的任务状态是进行中或准备中
      const runningTasks = all.filter(info => info.task && (info.task.status === 0 || info.task.status === 1));

      if (runningTasks.length > 0) {
        // 如果有进行中的任务，显示警告并阻止创建新任务
        const runningTaskIds = runningTasks.map(info => info.taskId.substring(0, 8)).join(', ');
        logger.value += `<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${runningTaskIds})，请等待任务完成后再操作</span><br/>`;
        return Promise.reject(new Error('选中内容与正在处理中的任务重叠'));
      }

      // 如果所有重叠的任务都不是进行中状态，删除这些控件并创建新任务
      logger.value += `<span class="log-item info">发现选区与${all.length}个已有任务重叠，重叠类型: ${overlapType}</span><br/>`;

      showLoading();
      try {
        // 删除所有重叠的控件
        await withVBAOperationWrapper('删除重叠控件', async () => {
          for (const overlappingInfo of all) {
            const { taskId: overlapTaskId, control: overlapControl, task: overlapTask, isEnhanced: overlapIsEnhanced } = overlappingInfo;

            logger.value += `<span class="log-item info">删除重叠的${overlapIsEnhanced ? '增强' : '普通'}任务 ${overlapTaskId.substring(0, 8)}，状态为 ${getTaskStatusText(overlapTask?.status || 0)}</span><br/>`;

            // 更新任务状态为已释放
            if (overlapTask) {
              overlapTask.status = 3; // Set to released
              overlapTask.terminated = true;
              overlapTask.errorMessage = '用户重新创建任务时删除';
            }

            // 删除控件但保留内容
            try {
              overlapControl.LockContents = false;
              overlapControl.Delete(false); // false表示保留内容

              if (map[overlapTaskId]) {
                map[overlapTaskId].placeholderRemoved = true;
              }
            } catch (error) {
              logger.value += `<span class="log-item error">删除重叠任务控件失败: ${error.message}</span><br/>`;
            }
          }
        });

        logger.value += `<span class="log-item success">已删除${all.length}个重叠的任务控件，准备创建新任务</span><br/>`;

      } catch (error) {
        hideLoading();
        logger.value += `<span class="log-item error">删除重叠任务控件失败: ${error.message}</span><br/>`;
        return Promise.reject(error);
      }
      hideLoading();
    }
    const uuid = getRandomValues().replace(/-/g, '').substring(0, 8);
    return new Promise(async (resolve, reject) => {
      try {
        // 递归查找属性 ContentControls 的路径
        const wps = window.wps;
        const currentDocument = getCurrentDocument(); // 或者类似的方式获取文档对象
        const selection = currentDocument.ActiveWindow.Selection;
        const selectionRange = selection.Range; // 获取当前选区
        const selectedText = selection.Text || '';
        selected.value = selectedText;
        copy();
        if (selectionRange) {
          const paras = selectionRange.Paragraphs;
          const firstPara = paras.Item(1);
          const lastPara = paras.Item(paras.Count);
          const start = firstPara.Range.Start;
          let end = lastPara.Range.End;
          const selectionStart = selectionRange.Start;
          const selectionEnd = selectionRange.End;
          let newSelectionRange = currentDocument.Range(Math.min(start, selectionStart), Math.max(end, selectionEnd));
          await withVBAOperationWrapper('创建内容控件', async () => {
            // 方法：先剪切选择的内容，然后在同一位置创建控件并粘贴内容
            // 1. 剪切选择的内容
            // selectionRange.Cut();
            // logger.value += `<span class="log-item info">已复制并删除原始选择内容</span><br/>`;
            // // selection.Delete();
            // // 2. 在剪切位置创建内容控件（此时光标在原选择位置）
            // selectionRange.Collapse(false); // 将光标移动到选区末尾
            let newContentControl = currentDocument.ContentControls.Add(wps.Enum.wdContentControlRichText, newSelectionRange);
            // 4. 粘贴之前剪切的内容
            if (!newContentControl) {
              // 剪切掉 selectRange，在下方新建一个控件，粘贴进去
              newContentControl = currentDocument.ContentControls.Add(wps.Enum.wdContentControlRichText);
              logger.value += `<span class="log-item error">创建内容控件失败</span><br/>`;
              const tableCount = newSelectionRange.Tables?.Count;
              if (tableCount) {
                const lastTable = newSelectionRange.Tables.Item(tableCount)
                const tableEnd = lastTable.Range.End;
                if (Math.abs(selectionEnd - tableEnd) <= 1) {
                  logger.value += `<span class="log-item info">检测到选区末尾是表格，使用特殊处理逻辑</span><br/>`;

                  // 1. 先剪切原始选区内容
                  logger.value += `<span class="log-item info">已剪切原始选区内容</span><br/>`;

                  // 2. 在表格下方插入段落
                  const tableEndRange = currentDocument.Range(tableEnd, tableEnd);
                  tableEndRange.InsertParagraphAfter();
                  logger.value += `<span class="log-item info">已在表格下方插入段落</span><br/>`;

                  // 3. 移动光标到新插入的段落
                  const newParagraphRange = currentDocument.Range(tableEnd + 1, tableEnd + 1);
                  const allRange = currentDocument.Range(start, tableEnd + 1);
                  newParagraphRange.Select();
                  newSelectionRange.Cut();

                  // 4. 在新段落位置创建控件
                  newContentControl = currentDocument.ContentControls.Add(wps.Enum.wdContentControlRichText);
                  allRange.Select()
                  if (!newContentControl) {
                    throw new Error('在新段落位置创建内容控件失败');
                  }

                  // 5. 粘贴内容到新控件
                  console.log(getCurrentDocument().Application.Selection.Text)
                  // getCurrentDocument().Application.Selection.Paste();
                  newContentControl.Range.Paste()
                  logger.value += `<span class="log-item info">已将内容粘贴到表格下方的新控件中</span><br/>`;
                }
              } else {
                if (!newContentControl) {
                  throw new Error('创建内容控件失败');
                }
                selectionRange.Cut();
                newContentControl.Range.Paste();
              }
              
            }
            logger.value += `<span class="log-item success">已将原始内容粘贴到控件中</span><br/>`;
            // 6. 设置内容控件的属性
            const isEnhanced = wordType === 'wps-enhance_analysis';
            // 根据是否为增强类型添加标识
            newContentControl.Title = isEnhanced ? `任务增强_${uuid}` : `任务_${uuid}`;  // 设置标题，用于后续查找
            // 锁定控件内容
            newContentControl.LockContents = true;

            // 将控件ID保存到任务数据中
            if (!map[uuid]) {
              map[uuid] = {};
            }
            map[uuid].contentControlId = newContentControl.ID;
            map[uuid].isEnhanced = isEnhanced;
            logger.value += `<span class="log-item info">已创建${isEnhanced ? '增强' : '普通'}内容控件并锁定选区</span><br/>`;

            // 验证控件内容是否正确
            const controlText = newContentControl.Range.Text;
            logger.value += `<span class="log-item success">控件内容验证通过，长度: ${controlText.length}</span><br/>`;
          });
        }

        // 复制当前选中的内容用于生成文档
        // copy();
        // 创建初始任务状态 - 在 generatedocx 之前创建，确保任务立即出现在列表顶部
        map[uuid] = {
          status: 0, // 初始状态为准备中
          startTime: Date.now(),
          wordType: wordType, // 存储wordType参数
          isEnhanced: wordType === 'wps-enhance_analysis', // 添加增强标识
          selectedText: selectedText // 保存选中的文本内容
        }
        logger.value += `<span class="log-item success">创建${wordType === 'wps-enhanced' ? '增强' : '普通'}任务: ${uuid}，类型: ${wordType}</span><br/>`

        // 检查是否为批量测试模式
        if (isBatchTestMode.value) {
          // 获取用户信息检查 orgId
          let userOrgId = null;
          try {
            const userInfoStr = window.Application.PluginStorage.getItem('user_info');
            if (userInfoStr) {
              const userInfo = JSON.parse(userInfoStr);
              userOrgId = userInfo?.orgs?.[0]?.orgId;
            }
          } catch (error) {
            logger.value += `<span class="log-item warning">获取用户信息失败: ${error.message}</span><br/>`;
          }

          if (userOrgId === 2) {
            // 批量测试模式：跳过 generateDocx，直接设置为等待批量完成状态
            logger.value += `<span class="log-item info">批量测试模式：跳过文件生成和上传，任务${uuid.substring(0, 8)}等待批量插入</span><br/>`;

            // 设置固定的测试文件路径
            map[uuid].resultFile = "C:\\ww-wps-addon\\Downloads\\682ad7b4bde591ec60b7cf56.wps.docx";
            map[uuid].status = 5; // 设置为等待批量完成状态

            // 文件保存完成，调用回调函数取消loading状态
            if (onFileSavedCallback) {
              onFileSavedCallback();
            }

            resolve(); // 直接完成
            return;
          } else {
            logger.value += `<span class="log-item warning">批量测试模式仅对 orgId 为 2 的用户可用，当前用户 orgId: ${userOrgId}</span><br/>`;
          }
        }

        await generatedocx(uuid);

        // 文件保存完成，调用回调函数取消loading状态
        if (onFileSavedCallback) {
          onFileSavedCallback();
        }

        // 更新任务状态为进行中
        if (map[uuid]) {
          map[uuid].status = 1;
        }

        if (!1) {
          // 测试模式：创建一个示例文件
          if (!map[uuid]) {
            map[uuid] = {}
          }

          // 生成测试文件路径
          const tempUrl = 'https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/681c897c3566ef5e67508eb5.wps.docx';

          // const oa = window.Application.OAAssist;
          // const tempFilePath = await oa.DownloadFile(tempUrl);
          map[uuid].resultFile = "C:\\ww-wps-addon\\Downloads\\682ad7b4bde591ec60b7cf56.wps.docx";
          resolve(); // Resolve the promise for test mode
        } else {
          try {
            // 将wordType参数传递给uploadoss函数
            const result = await uploadoss(uuid, wordType);

            // Resolve the promise when upload is successful
            if (result) {
              resolve();
            } else {
              // 如果uploadoss返回为undefined且任务状态没有被设置为错误，则认为是API调用失败
              if (map[uuid] && map[uuid].status === 1) {
                map[uuid].status = -1;
                map[uuid].errorMessage = 'API调用失败或超时';
                logger.value += `<span class="log-item error">任务${uuid.substring(0, 8)}失败</span><br/>`;
                tryRemoveTaskPlaceholderWithLoading(uuid);
              }
              reject(new Error('API调用失败或超时'));
            }
          } catch (error) {
            // 捕获任何可能的异常，确保任务状态被更新
            if (map[uuid]) {
              map[uuid].status = -1;
              map[uuid].errorMessage = `执行错误: ${error.message}`;
              logger.value += `<span class="log-item error">任务${uuid.substring(0, 8)}执行出错: ${error.message}</span><br/>`;
              tryRemoveTaskPlaceholderWithLoading(uuid);
            }
            reject(error);
          }
        }
      } catch (error) {
        reject(error);
      }
    });
  };

  // 校对功能函数
  const runCheck = async (onFileSavedCallback = null) => {
    const selectionInfo = isSelectionInTaskControl();
    if (selectionInfo) {
      const { primary, all } = selectionInfo;
      const { taskId, control, task, isEnhanced, overlapType } = primary;

      // 检查是否有任何重叠的控件对应的任务状态是进行中或准备中
      const runningTasks = all.filter(info => info.task && (info.task.status === 0 || info.task.status === 1));

      if (runningTasks.length > 0) {
        // 如果有进行中的任务，显示警告并阻止创建新任务
        const runningTaskIds = runningTasks.map(info => info.taskId.substring(0, 8)).join(', ');
        logger.value += `<span class="log-item warning">当前选中内容与正在处理中的校对任务重叠 (${runningTaskIds})，请等待任务完成后再操作</span><br/>`;
        return Promise.reject(new Error('选中内容与正在处理中的校对任务重叠'));
      }

      // 如果所有重叠的任务都不是进行中状态，删除这些控件并创建新任务
      logger.value += `<span class="log-item info">发现选区与${all.length}个已有任务重叠，重叠类型: ${overlapType}</span><br/>`;

      showLoading();
      try {
        // 删除所有重叠的控件
        await withVBAOperationWrapper('删除重叠校对控件', async () => {
          for (const overlappingInfo of all) {
            const { taskId: overlapTaskId, control: overlapControl, task: overlapTask, isEnhanced: overlapIsEnhanced } = overlappingInfo;

            logger.value += `<span class="log-item info">删除重叠的校对任务 ${overlapTaskId.substring(0, 8)}，状态为 ${getTaskStatusText(overlapTask?.status || 0)}</span><br/>`;

            // 更新任务状态为已释放
            if (overlapTask) {
              overlapTask.status = 3; // Set to released
              overlapTask.terminated = true;
              overlapTask.errorMessage = '用户重新创建校对任务时删除';
            }

            // 删除控件但保留内容
            try {
              overlapControl.LockContents = false;
              overlapControl.Delete(false); // false表示保留内容

              if (map[overlapTaskId]) {
                map[overlapTaskId].placeholderRemoved = true;
              }
            } catch (error) {
              logger.value += `<span class="log-item error">删除重叠校对任务控件失败: ${error.message}</span><br/>`;
            }
          }
        });
        logger.value += `<span class="log-item success">已删除${all.length}个重叠的校对任务控件，准备创建新校对任务</span><br/>`;

      } catch (error) {
        hideLoading();
        logger.value += `<span class="log-item error">删除重叠校对任务控件失败: ${error.message}</span><br/>`;
        return Promise.reject(error);
      }
      hideLoading();
    }

    const uuid = getRandomValues().replace(/-/g, '').substring(0, 8);
    return new Promise(async (resolve, reject) => {
      try {
        // 递归查找属性 ContentControls 的路径
        const wps = window.wps;
        const currentDocument = getCurrentDocument(); // 或者类似的方式获取文档对象
        const selection = currentDocument.ActiveWindow.Selection;
        const selectionRange = selection.Range; // 获取当前选区
        const selectedText = selection.Text || '';
        selected.value = selectedText;
        copy();
        if (selectionRange) {
          const paras = selectionRange.Paragraphs;
          const firstPara = paras.Item(1);
          const lastPara = paras.Item(paras.Count);
          const start = firstPara.Range.Start;
          const end = lastPara.Range.End;
          const selectionStart = selectionRange.Start;
          const selectionEnd = selectionRange.End;
          const newSelectionRange = currentDocument.Range(Math.min(start, selectionStart), Math.max(end, selectionEnd));
          await withVBAOperationWrapper('创建校对控件', async () => {
            let newContentControl = currentDocument.ContentControls.Add(wps.Enum.wdContentControlRichText, newSelectionRange);
            if (!newContentControl) {
              console.log('创建校对内容控件失败')
              newContentControl = currentDocument.ContentControls.Add(wps.Enum.wdContentControlRichText);
              if (!newContentControl) {
                logger.value += `<span class="log-item error">创建校对内容控件失败</span><br/>`;
                throw new Error('创建校对内容控件失败');
              }
              selectionRange.Cut();
              newContentControl.Range.Paste();
            }
            logger.value += `<span class="log-item success">已将原始内容粘贴到校对控件中</span><br/>`;

            // 设置校对任务的控件标题
            newContentControl.Title = `校对_${uuid}`;  // 设置标题，用于后续查找
            // 锁定控件内容
            newContentControl.LockContents = true;

            // 将控件ID保存到任务数据中
            if (!map[uuid]) {
              map[uuid] = {};
            }
            map[uuid].contentControlId = newContentControl.ID;
            map[uuid].isCheckTask = true; // 标记为校对任务
            logger.value += `<span class="log-item info">已创建校对内容控件并锁定选区</span><br/>`;

            // 验证控件内容是否正确
            const controlText = newContentControl.Range.Text;
            logger.value += `<span class="log-item success">校对控件内容验证通过，长度: ${controlText.length}</span><br/>`;
          });
        }

        // 创建初始任务状态 - 在 generatedocx 之前创建，确保任务立即出现在列表顶部
        map[uuid] = {
          status: 0, // 初始状态为准备中
          startTime: Date.now(),
          wordType: 'wps-check', // 校对任务的wordType
          isCheckTask: true, // 标记为校对任务
          selectedText: selectedText // 保存选中的文本内容
        }
        logger.value += `<span class="log-item success">创建校对任务: ${uuid}，类型: wps-check</span><br/>`

        await generatedocx(uuid);

        // 文件保存完成，调用回调函数取消loading状态
        if (onFileSavedCallback) {
          onFileSavedCallback();
        }

        // 更新任务状态为进行中
        if (map[uuid]) {
          map[uuid].status = 1;
        }

        // 测试模式：直接使用提供的JSON URL进行校对
        if (0) { // 改为 1 启用测试模式，改为 0 禁用测试模式
          try {
            logger.value += `<span class="log-item info">校对测试模式：直接使用指定的JSON URL</span><br/>`;

            // 使用提供的测试JSON URL
            const testJsonUrl = 'https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/fc7539b21810cd4f0f0fb620/ai/68522c18f02ee683e0545de5.wps.json';

            logger.value += `<span class="log-item info">正在下载校对结果: ${testJsonUrl}</span><br/>`;

            // 使用WPS的OAAssist下载文件
            const oa = window.Application.OAAssist;
            const downloadedFilePath = await oa.DownloadFile(testJsonUrl);

            logger.value += `<span class="log-item success">校对结果已下载到: ${downloadedFilePath}</span><br/>`;

            // 使用WPS文件系统API读取JSON文件内容
            const fs = window.Application.FileSystem;
            const jsonContent = fs.ReadFile(downloadedFilePath);
            const jsonData = JSON.parse(jsonContent);

            logger.value += `<span class="log-item info">开始处理校对JSON数据</span><br/>`;

            // 处理校对JSON数据并添加批注
            const processSuccess = await processCheckingJson(jsonData, uuid);

            if (processSuccess) {
              map[uuid].status = 2; // 标记为完成

              // 控件已被删除，不需要更新控件标题
              logger.value += `<span class="log-item success">校对任务${uuid.substring(0, 8)}已完成批注处理</span><br/>`;

              // 计算任务耗时
              const taskTime = getElapsedTime(map[uuid].startTime);
              logger.value += `<span class="log-item success">校对任务${uuid.substring(0, 8)}完成，总耗时${taskTime}</span><br/>`;

              resolve();
            } else {
              // 处理失败
              map[uuid].status = -1;
              map[uuid].errorMessage = 'JSON处理失败: 校对JSON处理失败';

              // 控件已被删除，不需要更新控件标题
              logger.value += `<span class="log-item error">校对任务${uuid.substring(0, 8)}处理失败</span><br/>`;
              reject(new Error('校对JSON处理失败'));
            }

          } catch (error) {
            logger.value += `<span class="log-item error">校对测试模式执行失败: ${error.message}</span><br/>`;
            if (error.message.includes('Unsupported protocol')) {
              logger.value += `<span class="log-item error">文件路径格式错误，无法读取文件</span><br/>`;
            }

            if (map[uuid]) {
              map[uuid].status = -1;
              map[uuid].errorMessage = `JSON处理失败: ${error.message}`;

              // 控件可能已被删除，不需要更新控件标题
              logger.value += `<span class="log-item error">校对任务${uuid.substring(0, 8)}处理失败</span><br/>`;
              tryRemoveTaskPlaceholderWithLoading(uuid);
            }
            reject(error);
          }
        } else {
          // 正常模式：通过API调用
          try {
            // 调用uploadoss函数，传递wps-check作为wordType
            const result = await uploadoss(uuid, 'wps-check');

            // Resolve the promise when upload is successful
            if (result) {
              resolve();
            } else {
              // 如果uploadoss返回为undefined且任务状态没有被设置为错误，则认为是API调用失败
              if (map[uuid] && map[uuid].status === 1) {
                map[uuid].status = -1;
                map[uuid].errorMessage = '校对API调用失败或超时';
                logger.value += `<span class="log-item error">校对任务${uuid.substring(0, 8)}失败</span><br/>`;
                tryRemoveTaskPlaceholderWithLoading(uuid);
              }
              reject(new Error('校对API调用失败或超时'));
            }
          } catch (error) {
            // 捕获任何可能的异常，确保任务状态被更新
            if (map[uuid]) {
              map[uuid].status = -1;
              map[uuid].errorMessage = `校对执行错误: ${error.message}`;
              logger.value += `<span class="log-item error">校对任务${uuid.substring(0, 8)}执行出错: ${error.message}</span><br/>`;
              tryRemoveTaskPlaceholderWithLoading(uuid);
            }
            reject(error);
          }
        }
      } catch (error) {
        reject(error);
      }
    });
  };

  const run2 = async () => {
    // @todo:
  };

  const getHeaderStatusClass = () => {
    if (getErrorTasksCount() > 0) return 'status-error';
    if (getRunningTasksCount() > 0) return 'status-running';
    if (getCompletedTasksCount() > 0) return 'status-completed';
    return '';
  };

  const getRunningTasksCount = () => {
    return Object.values(map).filter(task => task.status === 0 || task.status === 1).length;
  };

  const getCompletedTasksCount = () => {
    return Object.values(map).filter(task => task.status === 2).length;
  };

  const getReleasableTasksCount = () => {
    return Object.values(map).filter(task => task.status === 2 || task.status === 4).length;
  };

  const getErrorTasksCount = () => {
    return Object.values(map).filter(task => task.status === -1).length;
  };

  // 添加强制清除所有任务控件的函数
  const forceCleanAllTasks = () => {
    try {
      logger.value += `<span class="log-item info">开始强制清除所有任务控件...</span><br/>`;

      const wps = window.wps;
      const currentDocument = getCurrentDocument();

      if (!currentDocument) {
        logger.value += `<span class="log-item error">无法获取当前文档</span><br/>`;
        return;
      }

      // 清除所有任务在map中的记录
      let tasksFound = 0;

      // 先尝试通过map中记录的任务ID来清除控件
      Object.keys(map).forEach(tid => {
        try {
          // 标记为释放状态
          if (map[tid].status === 1) {
            map[tid].status = 3; // 3 for "已释放" (Released)
            map[tid].terminated = true;
            map[tid].errorMessage = '强制清除';
          }

          // 尝试删除对应的内容控件
          tryRemoveTaskPlaceholder(tid);
          tasksFound++;
        } catch (error) {
          logger.value += `<span class="log-item warning">清除任务${tid.substring(0, 8)}失败: ${error.message}</span><br/>`;
        }
      });

      // 再扫描文档中所有以"任务_"开头的内容控件并删除
      if (currentDocument.ContentControls && currentDocument.ContentControls.Count > 0) {
        // 从后向前遍历，避免删除控件后索引变化的问题
        for (let i = currentDocument.ContentControls.Count; i >= 1; i--) {
          try {
            const control = currentDocument.ContentControls.Item(i);
            if (control && control.Title && (
              control.Title.startsWith('任务_') ||
              control.Title.startsWith('任务增强_') ||
              control.Title.startsWith('已完成_') ||
              control.Title.startsWith('已完成增强_') ||
              control.Title.startsWith('异常_') ||
              control.Title.startsWith('异常增强_') ||
              control.Title.startsWith('已停止_') ||
              control.Title.startsWith('已停止增强_')
            )) {
              try {
                // 解锁控件
                control.LockContents = false;
                // 删除控件但保留内容
                control.Delete(false);

                // 提取任务ID，根据前缀判断
                let tid;
                if (control.Title.startsWith('任务增强_')) {
                  tid = control.Title.substring(5); // 去掉"任务增强_"前缀
                } else if (control.Title.startsWith('任务_')) {
                  tid = control.Title.substring(3); // 去掉"任务_"前缀
                } else if (control.Title.startsWith('已完成增强_')) {
                  tid = control.Title.substring(6); // 去掉"已完成增强_"前缀
                } else if (control.Title.startsWith('已完成_')) {
                  tid = control.Title.substring(4); // 去掉"已完成_"前缀
                } else if (control.Title.startsWith('异常增强_')) {
                  tid = control.Title.substring(5); // 去掉"异常增强_"前缀
                } else if (control.Title.startsWith('异常_')) {
                  tid = control.Title.substring(3); // 去掉"异常_"前缀
                } else if (control.Title.startsWith('已停止增强_')) {
                  tid = control.Title.substring(6); // 去掉"已停止增强_"前缀
                } else if (control.Title.startsWith('已停止_')) {
                  tid = control.Title.substring(4); // 去掉"已停止_"前缀
                }

                // 如果这个任务不在map中，添加记录
                if (!map[tid]) {
                  map[tid] = {
                    status: 3, // 3 for "已释放" (Released)
                    terminated: true,
                    errorMessage: '强制清除',
                    placeholderRemoved: true
                  };
                } else {
                  map[tid].placeholderRemoved = true;
                  // 更新状态为已释放
                  map[tid].status = 3;
                }

                tasksFound++;
                logger.value += `<span class="log-item success">已删除任务${tid.substring(0, 8)}的内容控件</span><br/>`;
              } catch (error) {
                logger.value += `<span class="log-item error">删除控件失败: ${error.message}</span><br/>`;
              }
            }
          } catch (error) {
            logger.value += `<span class="log-item warning">访问控件时出错: ${error.message}</span><br/>`;
          }
        }
      }

      if (tasksFound > 0) {
        logger.value += `<span class="log-item success">已清除${tasksFound}个任务控件</span><br/>`;
      } else {
        logger.value += `<span class="log-item info">未发现任何任务控件</span><br/>`;
      }
    } catch (error) {
      logger.value += `<span class="log-item error">强制清除任务控件时出错: ${error.message}</span><br/>`;
    }
  };

  // 退出前清理所有URL监控任务
  const cleanupUrlMonitoringTasks = async () => {
    try {
      const monitoredUrls = Object.values(urlMonitorTasks).map(t => t.urlId);
      if (monitoredUrls.length > 0) {
        logger.value += `<span class="log-item info">清理${monitoredUrls.length}个URL监控任务...</span><br/>`;

        for (const urlId of monitoredUrls) {
          await stopUrlMonitoring(urlId);
        }
      }
    } catch (error) {
      logger.value += `<span class="log-item error">清理URL监控任务失败: ${error.message}</span><br/>`;
    }
  };

  const setupLifecycle = () => {
    onMounted(async () => {
      try {
        // 获取组织的appKey
        const userInfoStr = window.Application.PluginStorage.getItem('user_info');
        if (!userInfoStr) {
          throw new Error('未找到用户信息');
        }

        const userInfo = JSON.parse(userInfoStr);
        if (!userInfo.orgs || !userInfo.orgs[0]) {
          throw new Error('未找到组织信息');
        }
        appKeyInfo.appKey = userInfo.appKey;
        appKeyInfo.userName = userInfo.nickname;
        appKeyInfo.userId = userInfo.userId;
        appKeyInfo.appSecret = userInfo.appSecret;

      } catch (error) {
        logger.value += `<span class="log-item error">初始化appKey失败: ${error.message}</span><br/>`;
      }

      // 获取监控目录
      await fetchWatchedDir();

      // 添加监听学科和年级变化
      watch([subject, stage], async () => {
        await saveSubjectAndStage();
      }, { immediate: false });
      // 从服务器加载学科和年级选择
      await loadSubjectAndStage();

      // 监听版本变化，更新stageOptions
      const removeVersionListener = versionManager.onVersionChange(() => {
        // 根据版本更新年级选项
        const newOptions = updateStageOptions();

        // 清空原有选项并添加新选项
        stageOptions.splice(0, stageOptions.length, ...newOptions);

        // 更新当前选中的年级
        if (versionManager.isSeniorEdition() && stage.value === 'junior') {
          stage.value = 'senior';
        } else if (!versionManager.isSeniorEdition() && stage.value === 'senior') {
          stage.value = 'junior';
        }

        logger.value += `<span class="log-item info">版本变更，已更新年级选项为: ${versionManager.isSeniorEdition() ? '高中' : '初中'}</span><br/>`;
      });

      // 其他原有的初始化代码
      logger.value = '<span class="log-item">已加载任务窗格...</span><br/>'
      const app = window.Application
      const wdtask = window.taskpane
      const ui = window.wpsCustomTab
      getDocumentName()

      // 处理已有的任务
      await handle1();

      // 初始化WebSocket连接
      initWebSocket();

      // 设置配置更改监听器
      setupConfigChangeListener();

      // 开始检查任务状态
      runschedule()

      // 添加窗口关闭时的清理
      window.addEventListener('beforeunload', cleanupUrlMonitoringTasks);
      // 在组件卸载时清除定时器和版本监听器
      return () => {
        if (logSyncTimer) {
          clearTimeout(logSyncTimer);
        }
        if (removeVersionListener) {
          removeVersionListener();
        }
      };
    })
    // 设置日志监听器，实时同步到服务端
    watch(logger, (newValue) => {
      // 防抖处理，避免频繁发送
      if (logSyncTimer) {
        clearTimeout(logSyncTimer);
      }
      logSyncTimer = setTimeout(() => {
        syncLogToServer(newValue);
      }, 10); // 500ms 防抖
    }, { immediate: false });
  }

  // 添加监听服务端配置变更的事件处理
  const setupConfigChangeListener = () => {
    wsClient.addEventListener('config', (data) => {
      if (data.eventType === 'subjectAndStageChanged' && data.data) {
        // 仅当值不同时更新，避免循环
        if (data.data.subject !== subject.value) {
          subject.value = data.data.subject;
          logger.value += `<span class="log-item info">学科设置已从服务器同步: ${subject.value}</span><br/>`;
        }

        if (data.data.stage !== stage.value) {
          stage.value = data.data.stage;
          logger.value += `<span class="log-item info">年级设置已从服务器同步: ${stage.value}</span><br/>`;
        }
      }
    });
  };

  const isLoading = ref(false);

  const isSelectionInTaskControl = () => {
    try {
      const document = getCurrentDocument();
      const selection = document.ActiveWindow.Selection;

      if (!selection || !document || !document.ContentControls) {
        return null;
      }

      // Get the selection range
      const selRange = selection.Range;

      // 存储所有重叠的控件信息
      const overlappingControls = [];

      // Check each content control to see if selection overlaps with it
      for (let i = 1; i <= document.ContentControls.Count; i++) {
        try {
          const control = document.ContentControls.Item(i);

          // 更安全地检查 control
          if (!control) {
            continue;
          }

          // 安全地获取 Title，处理 null、undefined 的情况
          const title = (control.Title || '').trim();

          // 检查控件范围是否与选区重叠
          const controlRange = control.Range;
          if (selRange.Start < controlRange.End && selRange.End > controlRange.Start) {
            let taskId = null;
            let isEnhanced = false;
            let isEmptyTitle = false;

            // 如果 title 为空或trim后为空，标记为可覆盖的空标题控件
            if (!title) {
              isEmptyTitle = true;
              taskId = `empty_${control.ID || Date.now()}`; // 为空标题控件生成一个临时ID
            }
            // 如果有有效的任务前缀，按原逻辑处理
            else if (
              title.startsWith('任务_') ||
              title.startsWith('任务增强_') ||
              title.startsWith('校对_') ||
              title.startsWith('已完成_') ||
              title.startsWith('已完成增强_') ||
              title.startsWith('已完成校对_') ||
              title.startsWith('异常_') ||
              title.startsWith('异常增强_') ||
              title.startsWith('异常校对_') ||
              title.startsWith('已停止_') ||
              title.startsWith('已停止增强_') ||
              title.startsWith('已停止校对_')
            ) {
              if (title.startsWith('任务增强_')) {
                taskId = title.substring(5); // Remove '任务增强_' prefix
                isEnhanced = true;
              } else if (title.startsWith('任务_')) {
                taskId = title.substring(3); // Remove '任务_' prefix
              } else if (title.startsWith('校对_')) {
                taskId = title.substring(3); // Remove '校对_' prefix
              } else if (title.startsWith('已完成增强_')) {
                taskId = title.substring(6); // Remove '已完成增强_' prefix
                isEnhanced = true;
              } else if (title.startsWith('已完成校对_')) {
                taskId = title.substring(6); // Remove '已完成校对_' prefix
              } else if (title.startsWith('已完成_')) {
                taskId = title.substring(4); // Remove '已完成_' prefix
              } else if (title.startsWith('异常增强_')) {
                taskId = title.substring(5); // Remove '异常增强_' prefix
                isEnhanced = true;
              } else if (title.startsWith('异常校对_')) {
                taskId = title.substring(5); // Remove '异常校对_' prefix
              } else if (title.startsWith('异常_')) {
                taskId = title.substring(3); // Remove '异常_' prefix
              } else if (title.startsWith('已停止增强_')) {
                taskId = title.substring(6); // Remove '已停止增强_' prefix
                isEnhanced = true;
              } else if (title.startsWith('已停止校对_')) {
                taskId = title.substring(6); // Remove '已停止校对_' prefix
              } else if (title.startsWith('已停止_')) {
                taskId = title.substring(4); // Remove '已停止_' prefix
              }
            }
            // 如果既不是空标题也不是任务控件，跳过
            else {
              continue;
            }

            // 如果有有效的 taskId（包括空标题的临时ID），则添加到结果中
            if (taskId) {
              // 计算重叠类型
              let overlapType;
              if (selRange.Start >= controlRange.Start && selRange.End <= controlRange.End) {
                overlapType = 'completely_within'; // 选区完全在控件内
              } else if (selRange.Start <= controlRange.Start && selRange.End >= controlRange.End) {
                overlapType = 'completely_contains'; // 选区完全包含控件
              } else {
                overlapType = 'partial_overlap'; // 部分重叠
              }

              overlappingControls.push({
                taskId,
                control,
                task: isEmptyTitle ? null : (map[taskId] || null), // 空标题控件没有对应的任务
                isEnhanced,
                isEmptyTitle, // 标记是否为空标题控件
                overlapType,
                controlRange: {
                  start: controlRange.Start,
                  end: controlRange.End
                },
                selectionRange: {
                  start: selRange.Start,
                  end: selRange.End
                }
              });
            }
          }
        } catch (e) {
          continue; // Skip errors on individual controls
        }
      }

      // 如果没有重叠的控件，返回 null
      if (overlappingControls.length === 0) {
        return null;
      }

      // 如果有重叠的控件，返回第一个（按文档中的顺序）
      // 也可以根据需要返回所有重叠的控件
      return {
        primary: overlappingControls[0], // 主要的重叠控件
        all: overlappingControls // 所有重叠的控件
      };

    } catch (error) {
      logger.value += `<span class="log-item error">检查选区位置出错: ${error.message}</span><br/>`;
      return null;
    }
  };

  const showLoading = () => {
    isLoading.value = true;
  };

  const hideLoading = () => {
    isLoading.value = false;
  };

  const tryRemoveTaskPlaceholderWithLoading = async (tid, deleteControl = false) => {
    showLoading();
    try {
      await tryRemoveTaskPlaceholder(tid, deleteControl);
    } finally {
      hideLoading();
    }
  };

  // 自定义弹窗相关方法
  const showConfirm = (message) => {
    return new Promise((resolve, reject) => {
      confirmDialog.show = true;
      confirmDialog.message = message;
      confirmDialog.resolveCallback = resolve;
      confirmDialog.rejectCallback = reject;
    });
  };

  const handleConfirm = (isConfirmed) => {
    confirmDialog.show = false;
    if (isConfirmed && confirmDialog.resolveCallback) {
      confirmDialog.resolveCallback(true);
    } else if (confirmDialog.resolveCallback) {
      confirmDialog.resolveCallback(false);
    }
    confirmDialog.resolveCallback = null;
    confirmDialog.rejectCallback = null;
  };

  // 错误弹窗相关方法
  const showErrorDialog = (title, message, type = 'error') => {
    errorDialog.show = true;
    errorDialog.title = title;
    errorDialog.message = message;
    errorDialog.type = type;
  };

  const hideErrorDialog = () => {
    errorDialog.show = false;
    errorDialog.title = '';
    errorDialog.message = '';
    errorDialog.type = 'error';
  };

  const loadSubjectAndStage = async () => {
    try {
      logger.value += `<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>`;

      const response = await wsClient.getSubjectAndStage();

      if (response.success && response.data) {
        if (response.data.subject) {
          subject.value = response.data.subject;
        }
        if (response.data.stage) {
          stage.value = response.data.stage;
        }
        logger.value += `<span class="log-item success">已从服务器加载学科(${subject.value})和年级(${stage.value})设置</span><br/>`;
      } else {
        logger.value += `<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>`;
      }
    } catch (error) {
      console.error('从服务器加载学科和年级设置失败:', error);
      logger.value += `<span class="log-item error">从服务器加载设置失败: ${error.message}</span><br/>`;
    }
  };

  const saveSubjectAndStage = async () => {
    try {
      if (subject.value || stage.value) {
        logger.value += `<span class="log-item info">正在保存学科(${subject.value})和年级(${stage.value})设置到服务器...</span><br/>`;

        const response = await wsClient.setSubjectAndStage(subject.value, stage.value);

        if (response && response.success) {
          logger.value += `<span class="log-item success">学科和年级设置已保存到服务器</span><br/>`;
        } else {
          logger.value += `<span class="log-item warning">保存学科和年级设置失败: ${response?.message || '未知错误'}</span><br/>`;
        }
      }
    } catch (error) {
      console.error('保存学科和年级到服务器失败:', error);
      logger.value += `<span class="log-item error">保存设置到服务器失败: ${error.message}</span><br/>`;
    }
  };

  // 检查企业ID并设置校对功能可见性
  const checkEnterpriseAndSetCheckingVisibility = async () => {
    try {
      // 这里可以通过API或其他方式获取当前用户的企业ID
      // 暂时使用模拟数据，实际项目中需要替换为真实的企业ID获取逻辑

      // 模拟从API获取企业ID的逻辑
      // const response = await wsClient.getUserInfo();
      // enterpriseId.value = response.data.enterpriseId;

      // 临时设置，实际使用时需要从用户信息中获取
      enterpriseId.value = 2; // 这里需要替换为实际的企业ID获取逻辑

      // 只有企业ID为2时才显示校对功能
      isCheckingVisible.value = enterpriseId.value === 2;

      if (isCheckingVisible.value) {
        logger.value += `<span class="log-item info">校对功能已启用（企业ID: ${enterpriseId.value}）</span><br/>`;
      } else {
        logger.value += `<span class="log-item info">校对功能不可用（企业ID: ${enterpriseId.value}）</span><br/>`;
      }
    } catch (error) {
      console.error('检查企业ID失败:', error);
      logger.value += `<span class="log-item error">检查企业ID失败: ${error.message}</span><br/>`;
      isCheckingVisible.value = false;
    }
  };

  // 新增方法：为指定范围添加批注
  const addCommentToRange = async (range, commentText) => {
    return await withVBAOperationWrapper('为范围添加批注', async () => {
      const document = getCurrentDocument();
      if (!document || !range) {
        return false;
      }

      // 为指定范围创建批注
      const comment = document.Comments.Add(range, commentText);
      // 设置批注内容的段落格式 - 行间距为1
      try {
        if (comment?.Range) {
          comment.Range.ParagraphFormat.Reset()
          comment.Range.ParagraphFormat.LineSpacingRule = 3;
          comment.Range.ParagraphFormat.LineSpacing = 10;
          comment.Edit()
        }
      } catch (formatError) {
        logger.value += `<span class="log-item warning">设置批注段落格式失败: ${formatError.message}</span><br/>`
      }

      return true;
    });
  };

  // 处理校对JSON数据下载和批注添加
  const processCheckingJson = async (jsonData, taskId) => {
    try {
      if (!jsonData || !Array.isArray(jsonData)) {
        logger.value += `<span class="log-item error">校对JSON数据格式错误：数据不是数组格式</span><br/>`;
        return false;
      }

      // 查找校对控件，但先不删除，保持其有效性
      const document = getCurrentDocument();
      let targetControl = null;
      let originalControlRange = null;

      // 查找校对控件
      if (document && document.ContentControls) {
        for (let i = 1; i <= document.ContentControls.Count; i++) {
          try {
            const control = document.ContentControls.Item(i);
            if (control?.Title === `校对_${taskId}`) {
              targetControl = control;
              originalControlRange = control.Range;
              logger.value += `<span class="log-item info">找到校对控件，准备添加批注</span><br/>`;
              break;
            }
          } catch (e) {
            continue; // 忽略单个控件错误
          }
        }
      }

      // 如果没有找到控件，尝试从任务信息中恢复范围
      if (!originalControlRange) {
        logger.value += `<span class="log-item warning">未找到校对控件，尝试从任务信息恢复范围</span><br/>`;
        const task = map[taskId];
        if (task && task.selectedText) {
          // 尝试在文档中查找原始选中的文本
          try {
            const find = document.Range().Find;
            find.ClearFormatting();
            find.Text = task.selectedText.substring(0, Math.min(task.selectedText.length, 100)); // 使用前100个字符来查找
            find.Forward = true;
            find.Wrap = 1; // wdFindContinue

            if (find.Execute()) {
              // 找到了匹配的文本，扩展范围到完整的选中内容
              const foundRange = find.Parent;
              if (task.selectedText.length > 100) {
                // 如果原始文本较长，扩展范围
                const extendedEnd = foundRange.Start + task.selectedText.length;
                originalControlRange = document.Range(foundRange.Start, Math.min(extendedEnd, document.Range().End));
              } else {
                originalControlRange = foundRange;
              }
              logger.value += `<span class="log-item info">通过文本查找定位到原始范围</span><br/>`;
            } else {
              logger.value += `<span class="log-item error">无法定位到原始控件范围</span><br/>`;
              return false;
            }
          } catch (findError) {
            logger.value += `<span class="log-item error">查找原始控件范围失败: ${findError.message}</span><br/>`;
            return false;
          }
        } else {
          logger.value += `<span class="log-item error">无法获取任务信息或选中文本</span><br/>`;
          return false;
        }
      }

      let processedCount = 0;
      let errorCount = 0;
      let fallbackComments = []; // 存储需要添加到整个范围的批注

      // 遍历最外层数组
      for (const dataItem of jsonData) {
        if (!dataItem.mode1 || !Array.isArray(dataItem.mode1)) {
          logger.value += `<span class="log-item warning">跳过无效数据项：缺少mode1数组</span><br/>`;
          continue;
        }

        // 遍历mode1数组中的每个题目
        for (const questionItem of dataItem.mode1) {
          if (!questionItem.error_info || !Array.isArray(questionItem.error_info)) {
            logger.value += `<span class="log-item warning">跳过无error_info的题目</span><br/>`;
            continue;
          }

          // 获取题目内容作为上下文
          const questContent = questionItem.quest_html || '';
          const questType = questionItem.quest_type || '';

          logger.value += `<span class="log-item info">处理${questType}题目，发现${questionItem.error_info.length}个错误信息</span><br/>`;

          // 遍历每个错误信息
          for (const errorInfo of questionItem.error_info) {
            try {
              // 构建批注内容
              let commentText = '';

              if (errorInfo.error_info) {
                commentText += `【错误类型】${errorInfo.error_info}\r`;
              }

              if (errorInfo.fix_info) {
                commentText += `【建议】${errorInfo.fix_info}`;
              }

              // 如果有关键词，尝试为关键词添加批注
              if (errorInfo.keywords && errorInfo.keywords.trim()) {
                // 重新获取控件的最新范围，确保范围有效
                let currentRange = targetControl ? targetControl.Range : originalControlRange;
                targetControl.LockContents = false;
                const success = addCommentToSelection(commentText, errorInfo.keywords.trim(), 0, targetControl.Range);
                if (success) {
                  processedCount++;
                  logger.value += `<span class="log-item success">已为关键词"${errorInfo.keywords.trim()}"添加批注: ${commentText}</span><br/>`;
                } else {
                  // 如果找不到关键词，将批注添加到待处理列表
                  fallbackComments.push({
                    comment: commentText,
                    keyword: errorInfo.keywords.trim()
                  });
                  logger.value += `<span class="log-item warning">关键词"${errorInfo.keywords.trim()}"未找到，将为整个范围添加批注</span><br/>`;
                }
              } else {
                // 如果没有具体关键词，添加到待处理列表
                fallbackComments.push({
                  comment: commentText,
                  keyword: null
                });
              }
            } catch (commentError) {
              errorCount++;
              logger.value += `<span class="log-item error">处理单个错误信息失败: ${commentError.message}</span><br/>`;
            }
          }
        }
      }
      // 处理需要添加到整个范围的批注
      if (fallbackComments.length > 0) {
        logger.value += `<span class="log-item info">为整个控件范围添加${fallbackComments.length}个批注</span><br/>`;
        for (const fallbackComment of fallbackComments) {
          try {
            let fullCommentText = fallbackComment.comment;
            // 重新获取控件的最新范围
            let currentRange = targetControl ? targetControl.Range : originalControlRange;
            // 需要先解锁，否则无法插入批注
            targetControl.LockContents = false;
            const success = addCommentToRange(targetControl.Range, fullCommentText);
            if (success) {
              processedCount++;
              logger.value += `<span class="log-item success">已为整个范围添加批注${fallbackComment.keyword ? `（关键词：${fallbackComment.keyword}）` : ''}</span><br/>`;
            } else {
              errorCount++;
            }
          } catch (rangeCommentError) {
            errorCount++;
            logger.value += `<span class="log-item error">为整个范围添加批注失败: ${rangeCommentError.message}</span><br/>`;
          }
        }
      }

      // // 所有批注添加完成后，删除原控件并重新创建
      // if (targetControl && processedCount > 0) {
      //   try {
      //     logger.value += `<span class="log-item info">所有批注添加完成，重新创建校对控件</span><br/>`;

      //     // 保存控件的完整范围信息
      //     const finalRange = document.Range(targetControl.Range.Start, targetControl.Range.End);

      //     // 删除原控件但保留内容
      //     targetControl.LockContents = false;
      //     targetControl.Delete(false);
      //     logger.value += `<span class="log-item success">已删除原校对控件，保留内容</span><br/>`;

      //     const wps = window.wps;

      //     // 参考校对按钮的range处理逻辑，确保包含完整的段落边界
      //     const paras = finalRange.Paragraphs;
      //     const firstPara = paras.Item(1);
      //     const lastPara = paras.Item(paras.Count);
      //     const start = firstPara.Range.Start;
      //     const end = lastPara.Range.End;
      //     const selectionStart = finalRange.Start;
      //     const selectionEnd = finalRange.End;
      //     const newSelectionRange = document.Range(Math.min(start, selectionStart), Math.max(end, selectionEnd));

      //     // 参考校对按钮的创建控件逻辑
      //     let newContentControl = document.ContentControls.Add(wps.Enum.wdContentControlRichText, newSelectionRange);

      //     if (!newContentControl) {
      //       logger.value += `<span class="log-item warning">创建校对完成控件失败，尝试备用方案</span><br/>`;
      //       // 备用方案：先创建控件，再剪切粘贴内容
      //       newContentControl = document.ContentControls.Add(wps.Enum.wdContentControlRichText);
      //       if (!newContentControl) {
      //         logger.value += `<span class="log-item error">重新创建校对控件失败</span><br/>`;
      //       } else {
      //         // 剪切原始范围的内容（使用处理过的range）
      //         newSelectionRange.Cut();
      //         // 粘贴到新控件中
      //         newContentControl.Range.Paste();
      //         logger.value += `<span class="log-item info">使用备用方案创建校对控件并转移内容</span><br/>`;
      //       }
      //     }

      //     if (newContentControl) {
      //       // 设置控件标题为成功状态
      //       newContentControl.Title = `已完成校对_${taskId}`;
      //       newContentControl.LockContents = false; // 校对完成后不锁定内容，允许用户编辑

      //       logger.value += `<span class="log-item success">已重新创建校对控件，标题：已完成校对_${taskId.substring(0, 8)}</span><br/>`;
      //     } else {
      //       logger.value += `<span class="log-item warning">重新创建校对控件失败</span><br/>`;
      //     }
      //   } catch (controlError) {
      //     logger.value += `<span class="log-item error">重新创建控件失败: ${controlError.message}</span><br/>`;
      //   }
      // }

      if (processedCount > 0) {
        logger.value += `<span class="log-item success">校对任务${taskId.substring(0, 8)}处理完成：成功添加${processedCount}个批注</span><br/>`;
        if (errorCount > 0) {
          logger.value += `<span class="log-item warning">校对任务${taskId.substring(0, 8)}：${errorCount}个批注添加失败</span><br/>`;
        }
        targetControl.Title = `已完成校对_${taskId}`;
        return true;
      } else {
        logger.value += `<span class="log-item error">校对任务${taskId.substring(0, 8)}：未能成功添加任何批注</span><br/>`;
        return false;
      }
    } catch (error) {
      logger.value += `<span class="log-item error">处理校对JSON数据失败: ${error.message}</span><br/>`;
      return false;
    }
  };

  // 用于管理 VBA 操作的辅助函数
  const withVBAOperationWrapper = async (operationName, operation) => {
    const app = window.Application;
    if (!app) {
      throw new Error('无法获取 Application 对象');
    }

    // 保存原始状态
    const originalScreenUpdating = app.ScreenUpdating;
    let timeoutId = null;

    try {
      // 关闭屏幕更新
      app.ScreenUpdating = false;
      
      // 设置兜底定时器，确保10秒后必须恢复屏幕更新
      timeoutId = setTimeout(() => {
        try {
          if (window.Application) {
            window.Application.ScreenUpdating = true;
            logger.value += `<span class="log-item warning">兜底策略：强制恢复屏幕更新（${operationName}）</span><br/>`;
          }
        } catch (e) {
          console.error('兜底恢复屏幕更新失败:', e);
        }
      }, 3000); // 10秒超时

      // 开始撤销记录
      const document = getCurrentDocument();
      if (document && document.UndoRecord) {
        document.UndoRecord.StartCustomRecord(operationName);
      }

      // 执行操作
      const result = await operation();

      return result;
    } catch (error) {
      logger.value += `<span class="log-item error">${operationName}操作失败: ${error.message}</span><br/>`;
      throw error;
    } finally {
      // 清除兜底定时器
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      // 多重保障确保屏幕更新被恢复
      try {
        // 第一次尝试：正常恢复
        if (window.Application) {
          window.Application.ScreenUpdating = originalScreenUpdating !== false;
        }

        // 结束撤销记录
        const document = getCurrentDocument();
        if (document && document.UndoRecord) {
          document.UndoRecord.EndCustomRecord();
        }
      } catch (cleanupError) {
        logger.value += `<span class="log-item warning">清理VBA操作状态时出错: ${cleanupError.message}</span><br/>`;

        // 第二次尝试：强制恢复屏幕更新
        try {
          if (window.Application) {
            window.Application.ScreenUpdating = true;
            logger.value += `<span class="log-item info">强制恢复屏幕更新成功</span><br/>`;
          }
        } catch (forceError) {
          logger.value += `<span class="log-item error">强制恢复屏幕更新也失败: ${forceError.message}</span><br/>`;
          console.error('强制恢复屏幕更新失败:', forceError);
        }
      }

      // 延迟再次确认屏幕更新已恢复（最后的保险）
      setTimeout(() => {
        try {
          if (window.Application && window.Application.ScreenUpdating === false) {
            window.Application.ScreenUpdating = true;
            logger.value += `<span class="log-item warning">延迟检查发现屏幕更新仍被关闭，已强制开启</span><br/>`;
          }
        } catch (e) {
          console.error('延迟检查屏幕更新状态失败:', e);
        }
      }, 100); // 100ms后检查
    }
  };

  return {
    docName,
    selected,
    logger,
    map,
    watchedDir,
    subject,
    stage,
    subjectOptions,
    stageOptions,
    fetchWatchedDir,
    clearLog,
    getCurrentDocument,
    checkDocumentFormat, // 添加文档格式检查函数
    getTaskStatusClass,
    getTaskStatusText,
    getElapsedTime,
    terminateTask,
    stopTaskWithoutRemovingControl, // 添加新的函数
    run1,
    run2,
    runCheck, // 添加校对功能函数
    getHeaderStatusClass,
    getRunningTasksCount,
    getCompletedTasksCount,
    getReleasableTasksCount,
    getErrorTasksCount,
    setupLifecycle,
    navigateToTaskControl,
    forceCleanAllTasks,
    ws,
    wsMessages,
    initWebSocket,
    handleWatcherEvent,
    // URL监控相关
    urlMonitorTasks,
    monitorUrlForTask,
    stopUrlMonitoring,
    getUrlMonitorStatus,
    forceUrlCheck,
    cleanupUrlMonitoringTasks,
    tryRemoveTaskPlaceholder,
    isLoading, // Add loading state
    isSelectionInTaskControl, // Add function to check selection
    tryRemoveTaskPlaceholderWithLoading, // Add enhanced removal function
    showConfirm,
    handleConfirm,
    confirmDialog, // 添加confirmDialog状态
    errorDialog, // 添加errorDialog状态
    showErrorDialog, // 添加显示错误弹窗方法
    hideErrorDialog, // 添加隐藏错误弹窗方法
    loadSubjectAndStage, // 添加加载学科和年级函数
    saveSubjectAndStage, // 添加保存学科和年级函数
    // 校对功能相关
    enterpriseId, // 企业ID
    isCheckingVisible, // 校对功能可见性
    checkEnterpriseAndSetCheckingVisibility, // 检查企业ID并设置校对功能可见性
    processCheckingJson, // 处理校对JSON数据
    // 批量测试模式相关
    isBatchTestMode, // 批量测试模式状态
    batchInsertAllTasks, // 批量插入函数
    getBatchWaitingTasksCount, // 获取等待批量完成的任务数量
  }
}
