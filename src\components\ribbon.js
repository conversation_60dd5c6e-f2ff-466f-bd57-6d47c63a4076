import Util from './js/util.js'

function activeTab() {
  //启动WPS程序后，默认显示的工具栏选项卡为ribbon.xml中某一tab
  if (window.wps.ribbonUI)
    window.wps.ribbonUI.ActivateTab('wpsAddinTab');
}

//这个函数在整个wps加载项中是第一个执行的
function OnAddinLoad(ribbonUI) {
  if (typeof window.Application.ribbonUI != 'object') {
    window.Application.ribbonUI = ribbonUI
  }

  if (typeof window.Application.Enum != 'object') {
    // 如果没有内置枚举值
    window.Application.Enum = Util.WPS_Enum
  }

  //这几个导出函数是给外部业务系统调用的
  // window.openOfficeFileFromSystemDemo = SystemDemo.openOfficeFileFromSystemDemo
  // window.InvokeFromSystemDemo = SystemDemo.InvokeFromSystemDemo

  // window.Application.PluginStorage.setItem('EnableFlag', false) //往PluginStorage中设置一个标记，用于控制两个按钮的置灰
  // window.Application.PluginStorage.setItem('ApiEventFlag', false) //往PluginStorage中设置一个标记，用于控制ApiEvent的按钮label
  // setTimeout(activeTab, 1000)
  // window.Application.ApiEvent.AddApiEventListener("DocumentOpen", function(doc) {
  //   // 新增一个 taksPane
  //   // Application.CreateTaskpane
  //   const isProd = import.meta.env.PROD
  //   const taskpane = window.Application.CreateTaskPane((isProd ? 'http://wwwps.hexinedu.com/wps-addon-build' : Util.GetUrlPath())  + Util.GetRouterHash() + '/taskpane')
  //   // 存储文档与taskpane的关联关系
  //   const docId = doc.DocID
  //   const tsIdKey = `taskpane_id_${docId}`
  //   window.Application.PluginStorage.setItem(tsIdKey, taskpane.ID)
  //   console.log(docId, tsIdKey, taskpane.ID)
  // })

  // 添加文档激活事件监听，用于处理文档切换时taskpane的显示与隐藏
  window.Application.ApiEvent.AddApiEventListener("WindowActivate", function(doc) {
    // 隐藏所有taskpane
    // hideAllTaskpanes()
    // 获取当前文档对应的taskpane并显示
    const docId = doc.DocID
    const tsIdKey = `taskpane_id_${docId}`
    let tsId = window.Application.PluginStorage.getItem(tsIdKey)
    const count = window.Application.PluginStorage.length;
    if (count > 0) {
      for (let i = 0; i < count; i++) {
        const key = window.Application.PluginStorage.key(i)
        if (key.startsWith('taskpane_id_')) {
          const _tsId = window.Application.PluginStorage.getItem(key)
          if (_tsId !== tsId) {
            const taskpane = window.Application.GetTaskPane(_tsId)
            if (taskpane) {
              taskpane.Visible = false
            }
          }
        }
      }
    }


    if (tsId) {
      const taskpane = window.Application.GetTaskPane(tsId)
      if (taskpane) {
        taskpane.Visible = true
      }
    }
  })

  return true
}

// 隐藏所有taskpane的函数
function hideAllTaskpanes() {
  // 遍历所有已保存的taskpane ID，并将它们全部隐藏
  const allTaskpanes = window.Application.TaskPanes
  if (allTaskpanes && allTaskpanes.Count > 0) {
    for (let i = 0; i < allTaskpanes.Count; i++) {
      const taskpane = allTaskpanes.Item(i)
      if (taskpane) {
        taskpane.Visible = false
      }
    }
  }
}

var WebNotifycount = 0
function OnAction(control) {
  const eleId = control?.Id || 'btnShowTaskPane'
  switch (eleId) {
    case 'btnShowTaskPane':
      {
        const docId = window.Application.ActiveDocument.DocID
        const tsIdKey = `taskpane_id_${docId}`
        let tsId = window.Application.PluginStorage.getItem(tsIdKey)

        let taskpane
        if (!tsId) {
          // taskpane = window.Application.CreateTaskPane(Util.GetUrlPath() + Util.GetRouterHash() + '/taskpane')
          const isProd = import.meta.env.PROD
          taskpane = window.Application.CreateTaskPane((isProd ? 'http://wwwps.hexinedu.com/wps-addon-build' : Util.GetUrlPath())  + Util.GetRouterHash() + '/taskpane')
          let id = taskpane.ID
          window.Application.PluginStorage.setItem(tsIdKey, id)
        } else {
          taskpane = window.Application.GetTaskPane(tsId)
        }
        taskpane.Visible = true
        taskpane.Width = 900;
      }
      break
    case 'btnApiEvent':
      {
        let bFlag = window.Application.PluginStorage.getItem('ApiEventFlag')
        let bRegister = bFlag ? false : true
        window.Application.PluginStorage.setItem('ApiEventFlag', bRegister)
        if (bRegister) {
          window.Application.ApiEvent.AddApiEventListener('DocumentNew', 'ribbon.OnNewDocumentApiEvent')
        } else {
          window.Application.ApiEvent.RemoveApiEventListener('DocumentNew', 'ribbon.OnNewDocumentApiEvent')
        }

        window.Application.ribbonUI.InvalidateControl('btnApiEvent')
      }
      break
    case 'btnWebNotify':
      {
        let currentTime = new Date()
        let timeStr =
          currentTime.getHours() + ':' + currentTime.getMinutes() + ':' + currentTime.getSeconds()
        window.Application.OAAssist.WebNotify(
          '这行内容由wps加载项主动送达给业务系统，可以任意自定义, 比如时间值:' +
          timeStr +
          '，次数：' +
          ++WebNotifycount,
          true
        )
      }
      break
    default:
      break
  }
  return true
}

function GetImage(control) {
  const eleId = control.Id
  switch (eleId) {
    case 'btnShowMsg':
      return 'images/1.svg'
    case 'btnShowDialog':
      return 'images/2.svg'
    case 'btnShowTaskPane':
      return 'images/2.svg'
    default:
  }
  return 'images/newFromTemp.svg'
}

function OnGetEnabled(control) {
  const eleId = control.Id
  switch (eleId) {
    case 'btnShowMsg':
      return true
    case 'btnShowDialog': {
      let bFlag = window.Application.PluginStorage.getItem('EnableFlag')
      return bFlag
    }
    case 'btnShowTaskPane': {
      let bFlag = window.Application.PluginStorage.getItem('EnableFlag')
      return bFlag
    }
    default:
      break
  }
  return true
}

function OnGetVisible(control) {
  const eleId = control.Id
  console.log(eleId)
  return true
}

function OnGetLabel(control) {
  const eleId = control.Id
  switch (eleId) {
    case 'btnIsEnbable': {
      let bFlag = window.Application.PluginStorage.getItem('EnableFlag')
      return bFlag ? '按钮Disable' : '按钮Enable'
    }
    case 'btnApiEvent': {
      let bFlag = window.Application.PluginStorage.getItem('ApiEventFlag')
      return bFlag ? '清除新建文件事件' : '注册新建文件事件'
    }
  }
  return ''
}

function OnNewDocumentApiEvent(doc) {
  alert('新建文件事件响应，取文件名: ' + doc.Name)
}

function OnTabActivate(tab) {
  if (tab.Id === 'wpsAddinTab') {
    // const docId = window.Application.ActiveDocument.DocID
    // const tsIdKey = `taskpane_id_${docId}`
    // let tsId = window.Application.PluginStorage.getItem(tsIdKey)
    // let taskpane
    // console.log(tsId)
    // if (!tsId) {
    //   // 创建新的taskpane并关联到当前文档
    //   const isProd = import.meta.env.PROD
    //   taskpane = window.Application.CreateTaskPane((isProd ? 'http://wwwps.hexinedu.com/wps-addon-build' : Util.GetUrlPath())  + Util.GetRouterHash() + '/taskpane')
    //   let id = taskpane.ID
    //   window.Application.PluginStorage.setItem(tsIdKey, id)
    // } else {
    //   taskpane = window.Application.GetTaskPane(tsId)
    // }
    // // 先隐藏所有taskpane，再显示当前文档对应的taskpane
    // // hideAllTaskpanes()
    // taskpane.Visible = true
  }
}

//这些函数是给wps客户端调用的
export default {
  OnAddinLoad,
  OnAction,
  GetImage,
  OnGetEnabled,
  OnGetVisible,
  OnGetLabel,
  OnNewDocumentApiEvent,
  OnTabActivate
}
