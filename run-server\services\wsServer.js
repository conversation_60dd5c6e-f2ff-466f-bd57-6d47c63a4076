const WebSocket = require('ws');
const { defaultLogger } = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');
const os = require('os');

class WsServer {
    constructor() {
        this.wss = null;
        this.clients = new Map(); // 改为Map以存储客户端元数据
        this.messageHandlers = new Map();
        this.logLevel = process.env.WS_LOG_LEVEL || 'info'; // 默认日志级别
        this.inactivityTimeout = 30 * 60 * 1000; // 30分钟不活动超时
        this.lastCleanupTime = Date.now();
        
        // 添加客户端断开连接的回调函数
        this.onClientDisconnected = null;
    }

    initialize(server) {
        this.wss = new WebSocket.Server({ server });
        
        this.wss.on('connection', (ws, req) => {
            // 分配唯一ID并记录连接元数据
            const clientId = uuidv4();
            const ipAddress = req.headers['x-forwarded-for'] || 
                             req.connection.remoteAddress || 
                             'unknown';
            
            // 添加客户端并记录元数据
            this.clients.set(ws, {
                id: clientId,
                ip: ipAddress,
                connectedAt: new Date(),
                lastActivity: new Date(),
                userAgent: req.headers['user-agent'] || 'unknown',
                messageCount: 0,
                bytesReceived: 0,
                bytesSent: 0
            });
            
            defaultLogger.info(`WebSocket客户端连接 [${clientId}] 来自 ${ipAddress}`);

            // 定期清理不活跃连接
            this.scheduleInactivityCheck();

            ws.on('message', (message) => {
                // 更新客户端活动时间和统计信息
                const clientInfo = this.clients.get(ws);
                if (clientInfo) {
                    clientInfo.lastActivity = new Date();
                    clientInfo.messageCount++;
                    clientInfo.bytesReceived += message.length;
                }
                
                try {
                    const parsedMessage = JSON.parse(message);
                    this.handleClientMessage(ws, parsedMessage);
                } catch (error) {
                    defaultLogger.error(`解析WebSocket消息失败 [${clientInfo?.id}]:`, error);
                    this.sendToClient(ws, {
                        type: 'error',
                        message: '无效的消息格式',
                        messageId: (() => {
                            try {
                                return JSON.parse(message).messageId;
                            } catch (e) {
                                return null;
                            }
                        })()
                    });
                }
            });

            ws.on('close', (code, reason) => {
                const clientInfo = this.clients.get(ws);
                if (clientInfo) {
                    const duration = Math.round((new Date() - clientInfo.connectedAt) / 1000);
                    defaultLogger.info(`WebSocket客户端断开连接 [${clientInfo.id}], 持续时间: ${duration}秒, 原因: ${reason || 'unknown'}, 代码: ${code || 'unknown'}`);
                    
                    // 如果设置了断开连接回调，调用它
                    if (typeof this.onClientDisconnected === 'function') {
                        try {
                            this.onClientDisconnected(clientInfo.id);
                        } catch (error) {
                            defaultLogger.error(`执行客户端断开连接回调时出错 [${clientInfo.id}]:`, error);
                        }
                    }
                }
                this.clients.delete(ws);
            });

            ws.on('error', (error) => {
                const clientInfo = this.clients.get(ws);
                defaultLogger.error(`WebSocket客户端错误 [${clientInfo?.id}]:`, error);
            });

            // 发送初始连接成功消息和版本信息
            this.sendToClient(ws, {
                type: 'connection',
                action: 'connected',
                success: true,
                message: 'WebSocket连接成功',
                timestamp: new Date().toISOString(),
                clientId: clientId // 返回客户端ID以便客户端记录
            });

            // 发送版本信息
            this.sendVersionInfo(ws);
        });

        defaultLogger.info('WebSocket服务器初始化完成');
        
        // 每5分钟打印一次连接统计
        setInterval(() => this.logConnectionStats(), 5 * 60 * 1000);
    }

    // 清理不活跃的连接
    scheduleInactivityCheck() {
        // 每5分钟最多执行一次清理
        const now = Date.now();
        if (now - this.lastCleanupTime < 5 * 60 * 1000) {
            return;
        }
        
        this.lastCleanupTime = now;
        const inactiveThreshold = now - this.inactivityTimeout;
        
        let inactiveCount = 0;
        this.clients.forEach((info, client) => {
            if (info.lastActivity.getTime() < inactiveThreshold) {
                defaultLogger.info(`关闭不活跃的WebSocket连接 [${info.id}], 最后活动: ${info.lastActivity.toISOString()}`);
                client.terminate();
                inactiveCount++;
            }
        });
        
        if (inactiveCount > 0) {
            defaultLogger.info(`已清理 ${inactiveCount} 个不活跃的WebSocket连接`);
        }
    }
    
    // 打印连接统计信息
    logConnectionStats() {
        const totalClients = this.clients.size;
        if (totalClients === 0) {
            return;
        }
        
        // 计算一些统计信息
        let totalMessages = 0;
        let totalBytesReceived = 0;
        let totalBytesSent = 0;
        const ipCounts = new Map();
        
        this.clients.forEach(info => {
            totalMessages += info.messageCount;
            totalBytesReceived += info.bytesReceived;
            totalBytesSent += info.bytesSent;
            
            const ip = info.ip;
            ipCounts.set(ip, (ipCounts.get(ip) || 0) + 1);
        });
        
        // 获取前3个最常见IP
        const topIps = Array.from(ipCounts.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 3)
            .map(([ip, count]) => `${ip}(${count})`);
            
        defaultLogger.info(`WebSocket连接统计: ${totalClients}个客户端, ${totalMessages}条消息, 接收${(totalBytesReceived/1024).toFixed(1)}KB, 发送${(totalBytesSent/1024).toFixed(1)}KB, 主要IP: ${topIps.join(', ')}`);
    }

    handleClientMessage(client, message) {
        const { type, action, data, messageId } = message;
        const clientInfo = this.clients.get(client);
        
        // 简化日志记录，非健康检查消息只在debug级别记录
        if (this.logLevel === 'debug' || type === 'health') {
            // 健康检查消息只记录简单摘要，不输出完整内容
            if (type === 'health') {
                // defaultLogger.debug(`收到health消息 [${clientInfo?.id}]: action=${action}, messageId=${messageId}`);
            } else {
                // 只记录消息类型和操作
                // defaultLogger.debug(`收到WebSocket消息 [${clientInfo?.id}]: ${type}/${action}, messageId: ${messageId || 'none'}`);
            }
        }
        
        if (!type) {
            return this.sendToClient(client, {
                type: 'error',
                messageId: messageId,
                message: '消息缺少type字段'
            });
        }

        const handler = this.messageHandlers.get(type);
        if (handler) {
            try {
                // 将messageId添加到data对象中传递给处理器
                const dataWithMessageId = { 
                    ...(data || {}), 
                    messageId 
                };
                handler(client, action, dataWithMessageId);
            } catch (error) {
                defaultLogger.error(`处理${type}类型消息时出错 [${clientInfo?.id}]:`, error);
                this.sendToClient(client, {
                    type: 'error',
                    messageId: messageId,
                    action: action,
                    message: `处理${type}消息失败: ${error.message}`
                });
            }
        } else {
            this.sendToClient(client, {
                type: 'error',
                messageId: messageId,
                message: `未知的消息类型: ${type}`
            });
        }
    }

    registerMessageHandler(type, handler) {
        this.messageHandlers.set(type, handler);
        defaultLogger.info(`注册了消息处理器: ${type}`);
    }

    broadcast(data) {
        const message = JSON.stringify(data);
        let successCount = 0;
        
        this.clients.forEach((info, client) => {
            if (client.readyState === WebSocket.OPEN) {
                try {
                    client.send(message);
                    // 更新统计信息
                    info.bytesSent += message.length;
                    successCount++;
                } catch (error) {
                    defaultLogger.error(`广播WebSocket消息失败 [${info.id}]: ${data.type}/${data.eventType || ''}`, error);
                }
            }
        });
        
        // 简化广播日志
        if (this.clients.size > 0) {
            defaultLogger.debug(`广播WebSocket消息: ${data.type}/${data.eventType || ''}, 发送到 ${successCount}/${this.clients.size} 个客户端`);
        }
    }

    standardizeResponse(data) {
        const standardData = {
            ...data,
            type: data.type || 'unknown',
            action: data.action || 'unknown',
            timestamp: data.timestamp || new Date().toISOString()
        };
        
        if (!data.eventType && typeof data.success === 'undefined') {
            standardData.success = true;
        }
        
        return standardData;
    }

    sendToClient(client, data) {
        const standardData = this.standardizeResponse(data);
        const clientInfo = this.clients.get(client);
        
        if (client.readyState === WebSocket.OPEN) {
            try {
                const jsonData = JSON.stringify(standardData);
                
                // 健康检查消息只记录简单摘要
                if (standardData.type === 'health' && this.logLevel === 'debug') {
                    defaultLogger.debug(`发送health消息 [${clientInfo?.id}]: messageId=${standardData.messageId}, action=${standardData.action}`);
                }
                
                client.send(jsonData);
                
                // 更新统计信息
                if (clientInfo) {
                    clientInfo.bytesSent += jsonData.length;
                }
                
                // 简化发送日志
                if (this.logLevel === 'debug' && standardData.type !== 'health') {
                    const logMessageId = standardData.messageId ? `, messageId: ${standardData.messageId}` : '';
                    defaultLogger.debug(`WebSocket消息已发送 [${clientInfo?.id}]: ${standardData.type}/${standardData.action || ''}${logMessageId}`);
                }
                return true;
            } catch (error) {
                defaultLogger.error(`WebSocket消息发送失败 [${clientInfo?.id}]: ${standardData.type}/${standardData.action || ''}`, error);
                return false;
            }
        } else {
            // 简化未发送日志
            if (this.logLevel === 'debug') {
                defaultLogger.debug(`WebSocket消息未发送 [${clientInfo?.id}]: 客户端连接状态不是OPEN (${client.readyState})`);
            }
            return false;
        }
    }

    sendWatcherEvent(eventType, data, messageId = null) {
        this.broadcast({
            type: 'watcher',
            eventType,
            data,
            messageId,
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * 向特定客户端发送监控事件
     * @param {string} eventType - 事件类型
     * @param {Object} data - 事件数据
     * @param {string} clientId - 目标客户端ID
     * @param {string} messageId - 可选的消息ID
     * @returns {boolean} 是否成功发送
     */
    sendWatcherEventToClient(eventType, data, clientId, messageId = null) {
        if (!clientId) {
            defaultLogger.warn('向客户端发送监控事件时缺少客户端ID');
            return false;
        }
        
        let targetClient = null;
        // 查找客户端ID对应的WebSocket连接
        for (const [client, info] of this.clients.entries()) {
            if (info.id === clientId) {
                targetClient = client;
                break;
            }
        }
        
        if (!targetClient) {
            defaultLogger.warn(`未找到客户端ID为 ${clientId} 的连接`);
            return false;
        }
        
        const message = {
            type: 'watcher',
            eventType,
            data,
            messageId,
            timestamp: new Date().toISOString()
        };
        return this.sendToClient(targetClient, message);
    }
    
    sendResponse(client, type, action, data, success = true, messageId = null) {
        return this.sendToClient(client, {
            type,
            action,
            success,
            messageId,
            ...(typeof data === 'object' ? data : { data })
        });
    }
    
    isRunning() {
        return this.wss !== null;
    }
    
    getClientCount() {
        return this.clients.size;
    }
    
    // 增强客户端详情信息
    getClientDetails() {
        const details = [];
        this.clients.forEach((info, client) => {
            const connectionDuration = Math.round((new Date() - info.connectedAt) / 1000);
            const inactivityDuration = Math.round((new Date() - info.lastActivity) / 1000);
            
            details.push({
                id: info.id,
                ip: info.ip,
                userAgent: info.userAgent,
                connectedAt: info.connectedAt.toISOString(),
                connectionDuration: `${connectionDuration}秒`,
                lastActivity: info.lastActivity.toISOString(),
                inactivityDuration: `${inactivityDuration}秒`,
                readyState: client.readyState,
                messageCount: info.messageCount,
                bytesReceived: info.bytesReceived,
                bytesSent: info.bytesSent,
                bufferedAmount: client.bufferedAmount
            });
        });
        return details;
    }
    
    // 获取更详细的统计信息
    getStats() {
        // 系统信息
        const systemInfo = {
            hostname: os.hostname(),
            platform: process.platform,
            arch: process.arch,
            cpus: os.cpus().length,
            totalMem: Math.round(os.totalmem() / (1024 * 1024)),
            freeMem: Math.round(os.freemem() / (1024 * 1024)),
            uptime: Math.round(os.uptime()),
            processUptime: Math.round(process.uptime())
        };
        
        // 连接统计
        let activeConnections = 0;
        let totalMessages = 0;
        let totalBytesReceived = 0;
        let totalBytesSent = 0;
        const ipStats = new Map();
        
        this.clients.forEach(info => {
            activeConnections++;
            totalMessages += info.messageCount;
            totalBytesReceived += info.bytesReceived;
            totalBytesSent += info.bytesSent;
            
            // 统计IP
            const ip = info.ip;
            if (!ipStats.has(ip)) {
                ipStats.set(ip, { count: 0, messages: 0 });
            }
            const ipStat = ipStats.get(ip);
            ipStat.count++;
            ipStat.messages += info.messageCount;
        });
        
        // 转换IP统计为数组
        const ipArray = Array.from(ipStats.entries()).map(([ip, stats]) => ({
            ip,
            connections: stats.count,
            messages: stats.messages
        }));
        
        return {
            timestamp: new Date().toISOString(),
            system: systemInfo,
            connections: {
                active: activeConnections,
                totalMessages,
                totalBytesReceived,
                totalBytesSent,
                ips: ipArray
            },
            handlers: Array.from(this.messageHandlers.keys())
        };
    }
    
    // 设置日志级别
    setLogLevel(level) {
        const validLevels = ['error', 'warn', 'info', 'debug'];
        if (validLevels.includes(level)) {
            this.logLevel = level;
            defaultLogger.info(`WebSocket日志级别已设置为: ${level}`);
            return true;
        }
        return false;
    }

    /**
     * 发送版本信息给指定客户端
     * @param {WebSocket} client - 目标客户端
     */
    sendVersionInfo(client) {
        // 从环境变量或配置中获取版本信息
        const versionInfo = this.getVersionInfo();
        
        this.sendToClient(client, {
            type: 'version',
            action: 'info',
            success: true,
            data: versionInfo,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 向所有客户端广播版本信息
     */
    broadcastVersionInfo() {
        const versionInfo = this.getVersionInfo();
        
        this.broadcast({
            type: 'version',
            action: 'info',
            data: versionInfo,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 设置版本信息提供者
     * @param {Function} provider - 返回版本信息的函数
     */
    setVersionInfoProvider(provider) {
        this.versionInfoProvider = provider;
    }

    /**
     * 获取当前版本信息
     * @returns {Object} 版本信息对象
     */
    getVersionInfo() {
        // 如果有自定义的版本信息提供者，使用它
        if (this.versionInfoProvider && typeof this.versionInfoProvider === 'function') {
            try {
                return this.versionInfoProvider();
            } catch (error) {
                defaultLogger.error('获取版本信息失败:', error);
            }
        }

        // 默认版本信息
        return {
            edition: process.env.APP_EDITION || 'wanwei', // 版本类型：'wanwei' 或 'hexin'
            appVersion: process.env.APP_VERSION || '1.0.0',
            buildDate: process.env.BUILD_DATE || new Date().toISOString(),
            environment: process.env.NODE_ENV || 'development'
        };
    }

    close() {
        if (this.wss) {
            this.wss.close();
            this.wss = null;
            this.clients.clear();
            defaultLogger.info('WebSocket服务器已关闭');
        }
    }
}

// 导出单例
module.exports = new WsServer(); 
