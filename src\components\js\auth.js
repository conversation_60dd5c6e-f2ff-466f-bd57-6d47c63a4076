import axios from 'axios';
import logger from './logger';
import wsClient from './wsClient';
import { useNotification } from '../../composables/useNotification';

const BASE_URL = import.meta.env.PROD
  ? 'http://127.0.0.1:3000/uc-api'  // In production, this should be a relative path that will be handled by the web server
  : 'http://127.0.0.1:3000/uc-api';  // In development, this will be proxied through Vite to the Node.js server

const USER_KEY = 'user_info';

// Set withCredentials to true to send cookies with cross-origin requests
axios.defaults.withCredentials = true;

// Log request and response interceptors for debugging
axios.interceptors.request.use(
  config => {
    // Add detailed logging for requests to 127.0.0.1:3000
    if (config.url.includes('127.0.0.1:3000')) {
    } else {
    }
    return config;
  },
  error => {
    logger.error('API请求错误', error);
    return Promise.reject(error);
  }
);

axios.interceptors.response.use(
  response => {
    // Add detailed logging for responses from 127.0.0.1:3000
    if (response.config.url.includes('127.0.0.1:3000')) {

    } else {

    }
    return response;
  },
  error => {
    if (error.config && error.config.url.includes('127.0.0.1:3000')) {
      if (error.response) {
        logger.error(`API响应错误 (详细): ${error.config.url}`, {
          status: error.response.status,
          statusText: error.response.statusText,
          headers: error.response.headers,
          data: error.response.data,
          message: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString()
        });
      } else if (error.request) {
        // 请求已发出但没有收到响应
        logger.error(`API网络错误 (详细): ${error.config.url}`, {
          message: error.message,
          code: error.code,
          errno: error.errno,
          syscall: error.syscall,
          address: error.address,
          port: error.port,
          stack: error.stack,
          timestamp: new Date().toISOString(),
          request: {
            method: error.config.method,
            url: error.config.url,
            headers: error.config.headers,
            withCredentials: error.config.withCredentials,
            data: error.config.data,
            timeout: error.config.timeout
          }
        });
      } else {
        // 设置请求时出错
        logger.error(`API配置错误 (详细): ${error.config?.url || 'unknown'}`, {
          message: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString(),
          config: error.config
        });
      }
    } else if (error.response) {
      logger.error(`API响应错误: ${error.config?.url}`, {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    } else {
      console.trace(error)
      logger.error('API网络错误', error.message);
    }
    return Promise.reject(error);
  }
);

/**
 * Check if the user is logged in
 * @returns {Promise<boolean>} True if logged in, false otherwise
 */
export async function checkLoginStatus() {
  try {
    try {
      // 使用WebSocket请求替代HTTP请求
      const response = await wsClient.getAuthUserInfo();

      // 检查响应是否成功
      if (!response.success) {
        // 如果是 AI 编辑功能未开启的错误，抛出特定错误
        if (response.code === 'AI_EDIT_DISABLED') {
          throw new Error(`AI_EDIT_DISABLED:${response.message || '该企业尚未开启 AI 编辑功能，请联系您的企业管理员。'}`);
        }
        // 其他错误情况，继续执行后续逻辑
        throw new Error(response.message || '获取用户信息失败');
      }

      const userData = response?.data;

      if (userData?.userId && window.Application) {

        window.Application.PluginStorage.setItem(USER_KEY, JSON.stringify(userData));
        return true;
      }
      if (userData?.userId && localStorage) {

        localStorage.setItem(USER_KEY, JSON.stringify(userData));
        return true;
      }
    } catch (verifyError) {
      logger.warn('存储的凭证无效，需要重新登录', verifyError.message);
      console.log('Stored credentials are no longer valid, will try to login again');
    }

    // If no stored auth or verification failed, check login status normally


    // 使用WebSocket请求替代HTTP请求
    const response = await wsClient.getAuthUserInfo();

    // 检查响应是否成功
    if (!response.success) {
      // 如果是 AI 编辑功能未开启的错误，抛出特定错误
      if (response.code === 'AI_EDIT_DISABLED') {
        throw new Error(`AI_EDIT_DISABLED:${response.message || '该企业尚未开启 AI 编辑功能，请联系您的企业管理员。'}`);
      }
      // 其他错误情况
      logger.warn('获取用户信息失败', response.message);
      return false;
    }

    const userData = response.data;

    if (userData?.userId && window.Application) {

      window.Application.PluginStorage.setItem(USER_KEY, JSON.stringify(userData));
      return true;
    }
    if (userData?.userId && localStorage) {

      localStorage.setItem(USER_KEY, JSON.stringify(userData));
      return true;
    }

    return false;
  } catch (error) {
    logger.error('检查登录状态时出错', error);
    console.error('Error checking login status:', error);
    return false;
  }
}

/**
 * Get the current user info
 * @returns {Object|null} User data or null if not logged in
 */
export function getUserInfo() {
  try {
    const userStr = window.Application?.PluginStorage.getItem(USER_KEY);
    if (userStr) {
      const userData = JSON.parse(userStr);

      return userData;
    }

    // 如果插件存储获取失败，尝试从localStorage获取
    if (localStorage) {
      const localUserStr = localStorage.getItem(USER_KEY);
      if (localUserStr) {
        const userData = JSON.parse(localUserStr);

        return userData;
      }
    }

    logger.warn('未找到用户信息');
    return null;
  } catch (error) {
    logger.error('获取用户信息时出错', error);
    return null;
  }
}

/**
 * Login with username and password
 * @param {string} username - Email or phone
 * @param {string} password - Password
 * @returns {Promise<Object>} Login result
 */
export async function login(username, password) {
  try {


    // 使用WebSocket请求替代HTTP请求
    const response = await wsClient.login(username, password);

    if (response.success) {


      // 保存cookie数据
      if (response.cookies) {

        Object.entries(response.cookies).forEach(([name, value]) => {
          document.cookie = `${name}=${value};`;
        });
      }

      // 保存用户信息到本地存储
      if (response.data) {
        const userData = response.data;


        if (window.Application?.PluginStorage) {
          window.Application.PluginStorage.setItem(USER_KEY, JSON.stringify(userData));
        }

        if (localStorage) {
          localStorage.setItem(USER_KEY, JSON.stringify(userData));
        }
      } else {
        // 如果登录响应中没有用户数据，尝试获取
        try {

          const userInfoResponse = await wsClient.getAuthUserInfo();

          if (userInfoResponse.success && userInfoResponse.data) {
            const userData = userInfoResponse.data;


            if (window.Application?.PluginStorage) {
              window.Application.PluginStorage.setItem(USER_KEY, JSON.stringify(userData));
            }

            if (localStorage) {
              localStorage.setItem(USER_KEY, JSON.stringify(userData));
            }

            return { success: true, user: userData };
          }
        } catch (userInfoError) {
          logger.warn('获取详细用户信息失败', userInfoError);
          // 即使这里失败也会继续返回成功，因为登录本身还是成功的
        }
      }

      return { success: true, user: response.data };
    }

    logger.warn('登录失败', response.message);

    // 返回详细的错误信息，包括错误码
    return {
      success: false,
      message: response.message || '登录失败',
      code: response.code,
      data: response.data
    };
  } catch (error) {
    logger.error('登录请求异常', {
      message: error.message
    });
    return {
      success: false,
      message: error.message || '登录失败，请稍后重试'
    };
  }
}

/**
 * Logout user
 * @returns {Promise<boolean>} True if logout was successful
 */
export async function logout() {
  try {


    // 使用WebSocket请求替代HTTP请求
    await wsClient.logout();

    if (window.Application?.PluginStorage) {
      window.Application.PluginStorage.removeItem(USER_KEY);

    }

    if (localStorage) {
      localStorage.removeItem(USER_KEY);

    }


    return true;
  } catch (error) {
    logger.error('登出请求异常', error);
    console.error('Logout error:', error);
    return false;
  }
}

/**
 * Validate login form
 * @param {string} username - Email or phone
 * @param {string} password - Password
 * @returns {Object} Validation result
 */
export function validateLoginForm(username, password) {
  const errors = {};

  // Validate username (email or phone)
  if (!username) {
    errors.username = '请输入用户名';
  } else {
    // Check if it's a valid email or phone
    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(username);
    const isPhone = /^1[3-9]\d{9}$/.test(username);

    if (!isEmail && !isPhone) {
      errors.username = '请输入正确的手机号或邮箱';
    }
  }

  // Validate password
  if (!password) {
    errors.password = '请输入密码';
  } else if (password.length < 6) {
    errors.password = '密码长度不能少于6位';
  }

  const isValid = Object.keys(errors).length === 0;

  if (isValid) {

  } else {
    logger.warn('登录表单验证失败', errors);
  }

  return {
    isValid,
    errors
  };
}

/**
 * Refresh session
 * @returns {Promise<boolean>} True if refresh was successful, false otherwise
 */
export async function refreshSession() {
  try {


    // 使用WebSocket请求替代HTTP请求
    const response = await wsClient.refreshSession();

    if (response.success && response.data) {

      if (window.Application?.PluginStorage) {
        window.Application.PluginStorage.setItem(USER_KEY, JSON.stringify(response.data));
      }
      if (localStorage) {
        localStorage.setItem(USER_KEY, JSON.stringify(response.data));
      }

      // 设置cookies
      if (response.cookies) {

        Object.entries(response.cookies).forEach(([name, value]) => {
          document.cookie = `${name}=${value};`;
        });
      }
      return true;
    }
    logger.warn('会话刷新失败', response);
    return false;
  } catch (error) {
    logger.error('会话刷新请求异常', error);
    console.error('Session refresh error:', error);
    return false;
  }
}

/**
 * Check connectivity to the server using both HTTP and WebSocket
 * @returns {Promise<Object>} Connectivity result with details
 */
export async function checkServerConnectivity() {
  try {
    const startTime = Date.now();
    let httpSuccess = true;
    let wsSuccess = false;
    let httpError = null;
    let wsError = null;
    // 测试WebSocket连接
    try {
      // 尝试连接WebSocket（不创建新连接，使用现有客户端）
      const docId = window.Application?.Document?.DocID;
      const wsConnected = await wsClient.connect(docId);

      if (wsConnected) {
        // 尝试获取健康状态
        try {
          const wsHealthResponse = await wsClient.getHealthStatus();
          console.log('WebSocket健康检查响应', wsHealthResponse);

          if (wsHealthResponse?.status === 'ok') {
            wsSuccess = true;
          } else {
            logger.warn('WebSocket健康检查返回异常状态', {
              status: wsHealthResponse?.status
            });

            wsError = new Error('WebSocket健康检查返回异常状态');
          }
        } catch (wsHealthError) {
          logger.error('WebSocket健康请求失败', {
            message: wsHealthError.message
          });

          wsError = wsHealthError;
        }
      } else {
        logger.error('WebSocket连接失败');
        wsError = new Error('WebSocket连接失败');
      }
    } catch (wsConnectError) {
      logger.error('WebSocket连接异常', {
        message: wsConnectError.message
      });

      wsError = wsConnectError;
    }

    // 返回综合状态
    return {
      success: httpSuccess || wsSuccess,
      httpCheck: {
        success: httpSuccess,
        error: httpError ? httpError.message : null
      },
      wsCheck: {
        success: wsSuccess,
        error: wsError ? wsError.message : null
      },
      connectionTime: Date.now() - startTime
    };
  } catch (error) {
    logger.error('服务器连接性检查出错', {
      message: error.message,
      stack: error.stack
    });

    return {
      success: false,
      error: error.message
    };
  }
}

// 添加WebSocket事件监听
export function setupAuthEvents() {
  const notification = useNotification();
  return wsClient.addEventListener('auth', async (data) => {
    if (data.eventType === 'login-success') {

      notification.showNotify('登录成功', 'success');

      try {
        // 获取用户信息

        const response = await wsClient.getAuthUserInfo();

        if (response.success && response.data) {
          const userData = response.data;


          // 保存用户信息到本地存储
          if (window.Application?.PluginStorage) {
            window.Application.PluginStorage.setItem(USER_KEY, JSON.stringify(userData));
          }

          if (localStorage) {
            localStorage.setItem(USER_KEY, JSON.stringify(userData));
          }

          // 跳转到主页或仪表盘

          window.location.hash = '#/taskpane'; // 或者其他主页路径
        } else {
          logger.warn('登录成功但获取用户信息失败');
        }
      } catch (error) {
        logger.error('获取用户信息时出错', error);
      }
    } else if (data.eventType === 'logout') {

      notification.showNotify('检测到插件服务已登出', 'warning');
      // 清除本地存储
      if (window.Application?.PluginStorage) {
        window.Application.PluginStorage.removeItem(USER_KEY);
      }
      if (localStorage) {
        localStorage.removeItem(USER_KEY);
      }
      // 可以在这里执行登出后的全局操作
      window.location.hash = '#/login';
    }
  });
}
